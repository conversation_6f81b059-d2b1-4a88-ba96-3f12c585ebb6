#!/usr/bin/env bash

# Debug Script for Cursor Registration Process
# This script helps debug and troubleshoot the registration automation

set -euo pipefail

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# Debug mode flag
DEBUG_MODE=true

# Logging functions
log_debug() {
    if [ "$DEBUG_MODE" = true ]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Source required functions
source_functions() {
    if [ -f "5Cursor_resetgpt.sh" ]; then
        # Source browser automation functions
        source <(sed -n '/^setup_browser_automation()/,/^}/p' 5Cursor_resetgpt.sh)
        source <(sed -n '/^launch_browser_session()/,/^}/p' 5Cursor_resetgpt.sh)
        source <(sed -n '/^chrome_execute_js()/,/^}/p' 5Cursor_resetgpt.sh)
        source <(sed -n '/^chrome_navigate()/,/^}/p' 5Cursor_resetgpt.sh)
        source <(sed -n '/^chrome_wait_for_element()/,/^}/p' 5Cursor_resetgpt.sh)
        source <(sed -n '/^chrome_fill_form()/,/^}/p' 5Cursor_resetgpt.sh)
        
        # Source email functions
        source <(sed -n '/^get_temporary_email()/,/^}/p' 5Cursor_resetgpt.sh)
        
        # Source credential generation
        source <(sed -n '/^generate_random_credentials()/,/^}/p' 5Cursor_resetgpt.sh)
        source <(sed -n '/^generate_secure_password()/,/^}/p' 5Cursor_resetgpt.sh)
        
        # Source constants
        USER_AGENTS=(
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
        )
        
        log_info "Functions loaded successfully"
    else
        log_error "Enhanced script not found"
        exit 1
    fi
}

# Debug page content
debug_page_content() {
    local chrome_pid="$1"
    
    log_debug "Analyzing page content..."
    
    # Get page title
    local title=$(chrome_execute_js "document.title" 2>/dev/null)
    log_info "Page title: $(echo "$title" | grep -o '"value":"[^"]*"' | cut -d'"' -f4)"
    
    # Get current URL
    local url=$(chrome_execute_js "window.location.href" 2>/dev/null)
    log_info "Current URL: $(echo "$url" | grep -o '"value":"[^"]*"' | cut -d'"' -f4)"
    
    # Get all input fields
    local inputs=$(chrome_execute_js "
        var inputs = document.querySelectorAll('input');
        var result = [];
        for (var i = 0; i < inputs.length; i++) {
            result.push({
                name: inputs[i].name || 'unnamed',
                type: inputs[i].type || 'text',
                placeholder: inputs[i].placeholder || '',
                id: inputs[i].id || '',
                className: inputs[i].className || ''
            });
        }
        JSON.stringify(result);
    " 2>/dev/null)
    
    if [ -n "$inputs" ]; then
        log_info "Found input fields:"
        echo "$inputs" | grep -o '"name":"[^"]*"' | cut -d'"' -f4 | while read -r field; do
            log_debug "  - Input field: $field"
        done
    fi
    
    # Get all buttons
    local buttons=$(chrome_execute_js "
        var buttons = document.querySelectorAll('button');
        var result = [];
        for (var i = 0; i < buttons.length; i++) {
            result.push({
                text: buttons[i].textContent.trim(),
                type: buttons[i].type || 'button',
                className: buttons[i].className || ''
            });
        }
        JSON.stringify(result);
    " 2>/dev/null)
    
    if [ -n "$buttons" ]; then
        log_info "Found buttons:"
        echo "$buttons" | grep -o '"text":"[^"]*"' | cut -d'"' -f4 | while read -r button; do
            log_debug "  - Button: $button"
        done
    fi
    
    # Check for error messages
    local errors=$(chrome_execute_js "
        var errorElements = document.querySelectorAll('.error, .alert-danger, [class*=\"error\"], [class*=\"danger\"], .text-red-500, .text-danger');
        var result = [];
        for (var i = 0; i < errorElements.length; i++) {
            if (errorElements[i].textContent.trim()) {
                result.push(errorElements[i].textContent.trim());
            }
        }
        result.join(' | ');
    " 2>/dev/null)
    
    if [ -n "$errors" ] && [ "$errors" != '""' ]; then
        log_warn "Found error messages: $errors"
    fi
}

# Interactive debugging session
interactive_debug() {
    log_info "Starting interactive debugging session..."
    
    # Generate test credentials
    local credentials=$(generate_random_credentials)
    IFS=':' read -r first_name last_name random_num <<< "$credentials"
    local email=$(get_temporary_email)
    local password=$(generate_secure_password)
    
    log_info "Generated test credentials:"
    log_info "  Name: $first_name $last_name"
    log_info "  Email: $email"
    log_info "  Password: $password"
    
    # Setup browser
    local profile_dir=$(setup_browser_automation)
    if [ $? -ne 0 ]; then
        log_error "Failed to setup browser"
        return 1
    fi
    
    # Launch browser
    local chrome_pid=$(launch_browser_session "$profile_dir")
    if [ $? -ne 0 ]; then
        log_error "Failed to launch browser"
        rm -rf "$profile_dir"
        return 1
    fi
    
    log_info "Browser launched successfully (PID: $chrome_pid)"
    log_info "Remote debugging available at: http://localhost:9222"
    
    # Navigate to Cursor registration page
    log_info "Navigating to Cursor registration page..."
    if chrome_navigate "https://authenticator.cursor.sh/sign-up" 20; then
        log_info "Navigation successful"
    else
        log_error "Navigation failed"
    fi
    
    # Debug page content
    debug_page_content "$chrome_pid"
    
    # Interactive menu
    while true; do
        echo
        echo -e "${CYAN}=== Debug Menu ===${NC}"
        echo "1) Analyze page content"
        echo "2) Try to fill email field"
        echo "3) Try to fill name fields"
        echo "4) Try to click submit button"
        echo "5) Execute custom JavaScript"
        echo "6) Navigate to different URL"
        echo "7) Take screenshot (if available)"
        echo "8) Show current page source"
        echo "9) Exit debug session"
        echo
        echo -n "Choose an option [1-9]: "
        read -r choice
        
        case $choice in
            1)
                debug_page_content "$chrome_pid"
                ;;
            2)
                log_info "Attempting to fill email field..."
                local email_selectors=(
                    'input[name="email"]'
                    'input[type="email"]'
                    'input[placeholder*="email" i]'
                    '#email'
                )
                
                for selector in "${email_selectors[@]}"; do
                    log_debug "Trying selector: $selector"
                    if chrome_wait_for_element "$selector" 3; then
                        if chrome_fill_form "$selector" "$email" "email"; then
                            log_info "Successfully filled email field with: $email"
                            break
                        fi
                    fi
                done
                ;;
            3)
                log_info "Attempting to fill name fields..."
                chrome_fill_form 'input[name="first_name"]' "$first_name" "text" || true
                chrome_fill_form 'input[name="firstName"]' "$first_name" "text" || true
                chrome_fill_form 'input[name="last_name"]' "$last_name" "text" || true
                chrome_fill_form 'input[name="lastName"]' "$last_name" "text" || true
                ;;
            4)
                log_info "Attempting to click submit button..."
                local submit_selectors=(
                    'button[type="submit"]'
                    'input[type="submit"]'
                    'button:contains("Sign up")'
                    '.submit-button'
                )
                
                for selector in "${submit_selectors[@]}"; do
                    log_debug "Trying selector: $selector"
                    if chrome_wait_for_element "$selector" 3; then
                        if chrome_fill_form "$selector" "" "click"; then
                            log_info "Successfully clicked submit button"
                            sleep 3
                            debug_page_content "$chrome_pid"
                            break
                        fi
                    fi
                done
                ;;
            5)
                echo -n "Enter JavaScript to execute: "
                read -r js_code
                if [ -n "$js_code" ]; then
                    local result=$(chrome_execute_js "$js_code" 2>/dev/null)
                    log_info "JavaScript result: $result"
                fi
                ;;
            6)
                echo -n "Enter URL to navigate to: "
                read -r url
                if [ -n "$url" ]; then
                    chrome_navigate "$url" 15
                    debug_page_content "$chrome_pid"
                fi
                ;;
            7)
                log_info "Screenshot functionality not implemented in this version"
                ;;
            8)
                log_info "Getting page source..."
                local source=$(chrome_execute_js "document.documentElement.outerHTML" 2>/dev/null)
                echo "$source" | head -50
                log_info "... (showing first 50 lines)"
                ;;
            9)
                log_info "Exiting debug session..."
                break
                ;;
            *)
                log_warn "Invalid choice. Please try again."
                ;;
        esac
    done
    
    # Cleanup
    log_info "Cleaning up..."
    kill $chrome_pid 2>/dev/null || true
    rm -rf "$profile_dir"
    log_info "Debug session ended"
}

# Quick test function
quick_test() {
    log_info "Running quick registration test..."
    
    # Generate test credentials
    local credentials=$(generate_random_credentials)
    IFS=':' read -r first_name last_name random_num <<< "$credentials"
    local email=$(get_temporary_email)
    local password=$(generate_secure_password)
    
    log_info "Test credentials: $first_name $last_name, $email"
    
    # Setup and launch browser
    local profile_dir=$(setup_browser_automation)
    local chrome_pid=$(launch_browser_session "$profile_dir")
    
    if [ $? -ne 0 ]; then
        log_error "Failed to launch browser"
        return 1
    fi
    
    # Navigate and test
    if chrome_navigate "https://authenticator.cursor.sh/sign-up" 20; then
        log_info "Navigation successful"
        debug_page_content "$chrome_pid"
        
        # Try to fill form automatically
        log_info "Attempting automatic form filling..."
        
        # Email field
        local email_filled=false
        local email_selectors=('input[name="email"]' 'input[type="email"]' '#email')
        for selector in "${email_selectors[@]}"; do
            if chrome_wait_for_element "$selector" 5; then
                if chrome_fill_form "$selector" "$email" "email"; then
                    log_info "Email field filled successfully"
                    email_filled=true
                    break
                fi
            fi
        done
        
        if [ "$email_filled" = false ]; then
            log_warn "Could not fill email field"
        fi
        
        # Submit button
        local submit_clicked=false
        local submit_selectors=('button[type="submit"]' 'input[type="submit"]')
        for selector in "${submit_selectors[@]}"; do
            if chrome_wait_for_element "$selector" 3; then
                if chrome_fill_form "$selector" "" "click"; then
                    log_info "Submit button clicked successfully"
                    submit_clicked=true
                    sleep 5
                    debug_page_content "$chrome_pid"
                    break
                fi
            fi
        done
        
        if [ "$submit_clicked" = false ]; then
            log_warn "Could not click submit button"
        fi
        
    else
        log_error "Navigation failed"
    fi
    
    # Cleanup
    kill $chrome_pid 2>/dev/null || true
    rm -rf "$profile_dir"
}

# Main function
main() {
    echo -e "${CYAN}========================================${NC}"
    echo -e "${CYAN}   Cursor Registration Debug Tool      ${NC}"
    echo -e "${CYAN}========================================${NC}"
    
    # Source functions
    source_functions
    
    echo
    echo "Choose debug mode:"
    echo "1) Interactive debugging session"
    echo "2) Quick automated test"
    echo "3) Exit"
    echo
    echo -n "Enter choice [1-3]: "
    read -r mode_choice
    
    case $mode_choice in
        1)
            interactive_debug
            ;;
        2)
            quick_test
            ;;
        3)
            log_info "Exiting debug tool"
            exit 0
            ;;
        *)
            log_error "Invalid choice"
            exit 1
            ;;
    esac
}

main "$@"
