[<PERSON><PERSON><PERSON>]
default_browser = chrome
chrome_path = /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
chrome_driver_path = /tmp/_MEItkfGbz/drivers/chromedriver
edge_path = /Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge
edge_driver_path = /tmp/_MEItkfGbz/drivers/msedgedriver
firefox_path = /Applications/Firefox.app/Contents/MacOS/firefox
firefox_driver_path = /tmp/_MEItkfGbz/drivers/geckodriver
brave_path = /Applications/Brave Browser.app/Contents/MacOS/Brave Browser
brave_driver_path = /tmp/_MEItkfGbz/drivers/chromedriver
opera_path = /Applications/Opera.app/Contents/MacOS/Opera
opera_driver_path = /tmp/_MEItkfGbz/drivers/chromedriver
operagx_path = /Applications/Opera GX.app/Contents/MacOS/Opera
operagx_driver_path = /tmp/_MEItkfGbz/drivers/chromedriver

[Turnstile]
handle_turnstile_time = 2
handle_turnstile_random_time = 1-3

[Timing]
min_random_time = 0.1
max_random_time = 0.8
page_load_wait = 0.1-0.8
input_wait = 0.3-0.8
submit_wait = 0.5-1.5
verification_code_input = 0.1-0.3
verification_success_wait = 2-3
verification_retry_wait = 2-3
email_check_initial_wait = 4-6
email_refresh_wait = 2-4
settings_page_load_wait = 1-2
failed_retry_time = 0.5-1
retry_interval = 8-12
max_timeout = 160

[Utils]
enabled_update_check = True
enabled_force_update = False
enabled_account_info = True

[OAuth]
show_selection_alert = False
timeout = 120
max_attempts = 3

[Token]
refresh_server = https://token.cursorpro.com.cn
enable_refresh = True

[Language]
current_language = 
fallback_language = en
auto_update_languages = True
language_cache_dir = /Users/<USER>/Desktop/cur_sor/.cursor-free-vip/language_cache

[MacPaths]
storage_path = /var/root/Library/Application Support/Cursor/User/globalStorage/storage.json
sqlite_path = /var/root/Library/Application Support/Cursor/User/globalStorage/state.vscdb
machine_id_path = /var/root/Library/Application Support/Cursor/machineId
cursor_path = /Applications/Cursor.app/Contents/Resources/app
updater_path = /var/root/Library/Application Support/cursor-updater
update_yml_path = /Applications/Cursor.app/Contents/Resources/app-update.yml
product_json_path = /Applications/Cursor.app/Contents/Resources/app/product.json

[TempMailPlus]
enabled = false
email = 
epin = 

