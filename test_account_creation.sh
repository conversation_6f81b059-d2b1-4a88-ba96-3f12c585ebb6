#!/usr/bin/env bash

# Test Account Creation for Fixed Enhanced Cursor Reset Tool
set -euo pipefail

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Logging functions
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_success() { echo -e "${CYAN}[SUCCESS]${NC} $1"; }

echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}    Account Creation Test Suite        ${NC}"
echo -e "${CYAN}========================================${NC}"

# Test credential generation
test_credentials() {
    echo -e "\n${BLUE}=== Testing Credential Generation ===${NC}"
    
    # Source the credential functions from the fixed script
    source <(sed -n '/^generate_random_credentials()/,/^}/p' 6Cursor_resetgpt_fixed.sh)
    source <(sed -n '/^generate_secure_password()/,/^}/p' 6Cursor_resetgpt_fixed.sh)
    source <(sed -n '/^generate_enhanced_uuid()/,/^}/p' 6Cursor_resetgpt_fixed.sh)
    source <(sed -n '/^get_temporary_email()/,/^}/p' 6Cursor_resetgpt_fixed.sh)
    
    # Define step function
    step() { echo -e "[Step] $1"; }
    
    # Test credential generation
    local credentials=$(generate_random_credentials)
    if [[ "$credentials" =~ ^[A-Za-z]+:[A-Za-z]+:[0-9]+$ ]]; then
        log_success "Credentials generated: $credentials"
    else
        log_error "Invalid credential format: $credentials"
        return 1
    fi
    
    # Test password generation
    local password=$(generate_secure_password 16)
    if [ ${#password} -eq 16 ]; then
        log_success "Password generated (length: ${#password})"
    else
        log_error "Password length incorrect: ${#password}"
        return 1
    fi
    
    # Test UUID generation
    local uuid=$(generate_enhanced_uuid)
    if [[ "$uuid" =~ ^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[0-9a-f]{4}-[0-9a-f]{12}$ ]]; then
        log_success "UUID generated: $uuid"
    else
        log_error "Invalid UUID format: $uuid"
        return 1
    fi
    
    # Test email generation
    local email=$(get_temporary_email)
    if [[ "$email" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
        log_success "Email generated: $email"
    else
        log_error "Invalid email format: $email"
        return 1
    fi
    
    return 0
}

# Test Chrome availability
test_chrome() {
    echo -e "\n${BLUE}=== Testing Chrome Availability ===${NC}"
    
    local chrome_path="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    if [ -f "$chrome_path" ]; then
        log_success "Google Chrome found"
        local version=$("$chrome_path" --version 2>/dev/null)
        log_info "Version: $version"
        return 0
    else
        log_error "Google Chrome not found"
        return 1
    fi
}

# Test AppleScript functionality
test_applescript() {
    echo -e "\n${BLUE}=== Testing AppleScript Functionality ===${NC}"
    
    if ! command -v osascript >/dev/null; then
        log_error "osascript not found"
        return 1
    fi
    
    log_success "osascript available"
    
    # Test basic AppleScript execution
    local test_result=$(osascript -e 'return "AppleScript works"' 2>/dev/null)
    if [ "$test_result" = "AppleScript works" ]; then
        log_success "Basic AppleScript execution works"
    else
        log_error "AppleScript execution failed"
        return 1
    fi
    
    # Test Chrome AppleScript interaction (if Chrome is running)
    if pgrep -f "Google Chrome" > /dev/null; then
        log_info "Chrome is running, testing AppleScript interaction..."
        local chrome_test=$(osascript -e 'tell application "Google Chrome" to return name' 2>/dev/null)
        if [ "$chrome_test" = "Google Chrome" ]; then
            log_success "Chrome AppleScript interaction works"
        else
            log_warn "Chrome AppleScript interaction may have issues"
        fi
    else
        log_info "Chrome not running, skipping AppleScript interaction test"
    fi
    
    return 0
}

# Test account database functions
test_database() {
    echo -e "\n${BLUE}=== Testing Account Database ===${NC}"
    
    # Create a test database
    local test_db="/tmp/test_cursor_accounts.txt"
    
    # Test saving account
    echo "=== Account Created: $(date) ===" > "$test_db"
    echo "Email: <EMAIL>" >> "$test_db"
    echo "Password: testpass123" >> "$test_db"
    echo "Name: John Doe" >> "$test_db"
    echo "================================" >> "$test_db"
    
    if [ -f "$test_db" ]; then
        local account_count=$(grep -c "Email:" "$test_db" 2>/dev/null || echo "0")
        if [ "$account_count" -eq 1 ]; then
            log_success "Account database test passed"
        else
            log_error "Account database test failed"
            return 1
        fi
    else
        log_error "Failed to create test database"
        return 1
    fi
    
    # Cleanup
    rm -f "$test_db"
    return 0
}

# Test browser automation setup
test_browser_setup() {
    echo -e "\n${BLUE}=== Testing Browser Automation Setup ===${NC}"
    
    # Source the browser setup function
    source <(sed -n '/^setup_browser_automation()/,/^}/p' 6Cursor_resetgpt_fixed.sh)
    
    # Define required logging functions
    _log() {
        local color_code="$1"
        local level_text="$2"
        local message="$3"
        echo -e "${color_code}[${level_text}]${NC} ${message}"
    }
    log_info() { _log "$GREEN" "INFO" "$1"; }
    log_error() { _log "$RED" "ERROR" "$1"; }
    log_success() { _log "$CYAN" "SUCCESS" "$1"; }
    step() { echo -e "[Step] $1"; }
    
    if setup_browser_automation; then
        log_success "Browser automation setup works"
        return 0
    else
        log_error "Browser automation setup failed"
        return 1
    fi
}

# Test the complete workflow (dry run)
test_workflow() {
    echo -e "\n${BLUE}=== Testing Complete Workflow (Dry Run) ===${NC}"
    
    log_info "This test simulates the account creation workflow without actually creating accounts"
    
    # Generate test credentials
    source <(sed -n '/^generate_random_credentials()/,/^}/p' 6Cursor_resetgpt_fixed.sh)
    source <(sed -n '/^generate_secure_password()/,/^}/p' 6Cursor_resetgpt_fixed.sh)
    source <(sed -n '/^get_temporary_email()/,/^}/p' 6Cursor_resetgpt_fixed.sh)
    
    step() { echo -e "[Step] $1"; }
    
    local credentials=$(generate_random_credentials)
    IFS=':' read -r first_name last_name random_num <<< "$credentials"
    local email=$(get_temporary_email)
    local password=$(generate_secure_password)
    
    log_info "Generated test credentials:"
    log_info "  Name: $first_name $last_name"
    log_info "  Email: $email"
    log_info "  Password: [HIDDEN]"
    
    # Simulate account creation steps
    log_info "✓ Credential generation"
    log_info "✓ Email generation"
    log_info "✓ Password generation"
    log_info "✓ Browser automation setup (simulated)"
    log_info "✓ Form filling (simulated)"
    log_info "✓ Account saving (simulated)"
    
    log_success "Workflow simulation completed successfully"
    return 0
}

# Main test execution
main() {
    local tests_passed=0
    local tests_total=6
    
    # Run all tests
    if test_credentials; then ((tests_passed++)); fi
    if test_chrome; then ((tests_passed++)); fi
    if test_applescript; then ((tests_passed++)); fi
    if test_database; then ((tests_passed++)); fi
    if test_browser_setup; then ((tests_passed++)); fi
    if test_workflow; then ((tests_passed++)); fi
    
    # Show results
    echo -e "\n${CYAN}========================================${NC}"
    echo -e "${CYAN}              Test Results              ${NC}"
    echo -e "${CYAN}========================================${NC}"
    echo -e "${GREEN}Passed: $tests_passed/$tests_total${NC}"
    
    if [ $tests_passed -eq $tests_total ]; then
        echo -e "\n${GREEN}🎉 All tests passed!${NC}"
        echo -e "${GREEN}The fixed enhanced script should work correctly.${NC}"
        echo -e "\n${YELLOW}To test account creation:${NC}"
        echo -e "${YELLOW}sudo ./6Cursor_resetgpt_fixed.sh${NC}"
        echo -e "${YELLOW}Then choose option 2 (Create new account only)${NC}"
        exit 0
    else
        echo -e "\n${YELLOW}⚠️  Some tests failed.${NC}"
        echo -e "${YELLOW}Please review the issues above before using the script.${NC}"
        exit 1
    fi
}

main "$@"
