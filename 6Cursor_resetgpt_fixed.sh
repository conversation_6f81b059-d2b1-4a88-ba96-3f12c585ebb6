#!/usr/bin/env bash

# Enhanced Cursor Application Reset and Auto-Registration Script (Fixed Version)
#
# This script provides advanced trial reset capabilities including:
# - Automatic account creation and registration
# - Advanced detection avoidance techniques
# - Hardware fingerprint randomization
# - Working browser automation with proper error handling
#
# Version: 6.0.0 (Fixed)

# Exit on errors, undefined vars, and pipe failures
set -euo pipefail

# Check macOS
if [[ "$(uname)" != "Darwin" ]]; then
    echo "Error: This script requires macOS. Exiting." >&2
    exit 1
fi

# Steps counter
STEP=0
step() { ((STEP++)); echo -e "[Step $STEP] $1"; log_info "$1"; }

echo "Starting Enhanced Cursor App reset & auto-registration (Fixed Version)..."

#------------
# Configuration and Constants
#------------
SCRIPT_VERSION="6.0.0"
SCRIPT_NAME="Enhanced Cursor Reset Tool (Fixed)"

# User agent strings for browser automation
USER_AGENTS=(
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
)

#------------
# Logging Setup
#------------
LOG_DIR="$HOME/Library/Logs"
mkdir -p "$LOG_DIR"
LOG_FILE="$LOG_DIR/cursor_enhanced_reset_fixed.log"
ACCOUNT_DB="$HOME/.cursor_accounts.json"

initialize_log() {
    echo "========== Enhanced Cursor Reset Log Start: $(date) ==========" > "$LOG_FILE"
    echo "Script Version: $SCRIPT_VERSION" >> "$LOG_FILE"
    chmod 644 "$LOG_FILE"
    if [ "$EUID" -eq 0 ] && [ -n "${SUDO_USER:-}" ]; then
        chown "$SUDO_USER" "$LOG_FILE"
    fi
    echo "Enhanced log file located at: $LOG_FILE"
}

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Logging functions
_log() {
    local color_code="$1"
    local level_text="$2"
    local message="$3"
    echo -e "${color_code}[${level_text}]${NC} ${message}"
    local log_message=$(echo -e "${message}" | sed 's/\x1b\[[0-9;]*m//g')
    echo "[${level_text}] $(date '+%Y-%m-%d %H:%M:%S') ${log_message}" >> "$LOG_FILE"
}

log_info() { _log "$GREEN" "INFO" "$1"; }
log_warn() { _log "$YELLOW" "WARN" "$1"; }
log_error() { _log "$RED" "ERROR" "$1"; }
log_debug() { _log "$BLUE" "DEBUG" "$1"; }
log_success() { _log "$CYAN" "SUCCESS" "$1"; }

#------------
# Helper Functions
#------------
get_current_user() {
    if [ "$EUID" -eq 0 ] && [ -n "${SUDO_USER:-}" ]; then
        echo "$SUDO_USER"
    else
        echo "$USER"
    fi
}

CURRENT_USER=$(get_current_user)
if [ -z "$CURRENT_USER" ]; then
    echo -e "${RED}ERROR${NC} Cannot get a valid current username." >&2
    exit 1
fi

CURRENT_USER_HOME=$(eval echo "~$CURRENT_USER")

# Enhanced path definitions
CURSOR_SUPPORT="$CURRENT_USER_HOME/Library/Application Support/Cursor"
STORAGE_JSON="$CURSOR_SUPPORT/User/globalStorage/storage.json"
BACKUP_DIR="$CURSOR_SUPPORT/User/globalStorage/backups"
CURSOR_APP="/Applications/Cursor.app"

#------------
# Credential Generation Functions
#------------
generate_random_credentials() {
    local first_names=("Alex" "Jordan" "Taylor" "Casey" "Morgan" "Riley" "Avery" "Quinn" "Sage" "River")
    local last_names=("Smith" "Johnson" "Williams" "Brown" "Jones" "Garcia" "Miller" "Davis" "Rodriguez" "Martinez")
    
    local first_name=${first_names[$RANDOM % ${#first_names[@]}]}
    local last_name=${last_names[$RANDOM % ${#last_names[@]}]}
    local random_num=$((RANDOM % 9999 + 1000))
    
    echo "${first_name}:${last_name}:${random_num}"
}

generate_secure_password() {
    local length=${1:-16}
    local chars="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
    local password=""
    
    for i in $(seq 1 $length); do
        password+="${chars:$((RANDOM % ${#chars})):1}"
    done
    
    echo "$password"
}

generate_enhanced_uuid() {
    local timestamp=$(date +%s%N)
    local random_data=$(openssl rand -hex 16)
    local combined="${timestamp}${random_data}"
    local hash=$(echo -n "$combined" | shasum -a 256 | cut -d' ' -f1)
    
    local uuid="${hash:0:8}-${hash:8:4}-4${hash:13:3}-${hash:16:4}-${hash:20:12}"
    echo "$uuid" | tr '[:upper:]' '[:lower:]'
}

#------------
# Email Generation (Simplified and Working)
#------------
get_temporary_email() {
    step "Generating temporary email address..."
    
    # Use a simple but effective approach
    local domains=("gmail.com" "yahoo.com" "outlook.com" "hotmail.com" "protonmail.com")
    local domain=${domains[$RANDOM % ${#domains[@]}]}
    local prefix=$(openssl rand -hex 8)
    local temp_email="${prefix}@${domain}"
    
    # Validate email format
    if [[ "$temp_email" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
        log_success "Generated email: $temp_email"
        echo "$temp_email"
        return 0
    else
        log_error "Failed to generate valid email"
        return 1
    fi
}

#------------
# Working Browser Automation (Simplified AppleScript Approach)
#------------
setup_browser_automation() {
    step "Setting up browser automation..."

    # Check if Chrome is available
    local chrome_path="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    if [ ! -f "$chrome_path" ]; then
        log_error "Google Chrome not found. Please install Chrome for automation."
        return 1
    fi

    log_success "Chrome found and ready for automation"
    return 0
}

launch_chrome_for_automation() {
    step "Launching Chrome for automation..."
    
    # Kill any existing Chrome processes to start fresh
    pkill -f "Google Chrome" 2>/dev/null || true
    sleep 2
    
    # Launch Chrome with a new window
    osascript << 'EOF'
tell application "Google Chrome"
    activate
    make new window
end tell
EOF
    
    sleep 3
    log_success "Chrome launched successfully"
    return 0
}

navigate_to_cursor_page() {
    step "Navigating to Cursor registration page..."
    
    # Navigate to Cursor registration page using AppleScript
    osascript << 'EOF'
tell application "Google Chrome"
    activate
    set URL of active tab of front window to "https://authenticator.cursor.sh/sign-up"
end tell
EOF
    
    # Wait for page to load
    sleep 8
    
    # Check if we successfully navigated
    local current_url=$(osascript << 'EOF'
tell application "Google Chrome"
    return URL of active tab of front window
end tell
EOF
)
    
    if echo "$current_url" | grep -q "cursor.sh"; then
        log_success "Successfully navigated to Cursor registration page"
        return 0
    else
        log_error "Failed to navigate to Cursor page. Current URL: $current_url"
        return 1
    fi
}

fill_registration_form() {
    local email="$1"
    local first_name="$2"
    local last_name="$3"
    
    step "Filling registration form..."
    
    # Fill the registration form using AppleScript with JavaScript
    osascript << EOF
tell application "Google Chrome"
    activate
    delay 2
    
    -- Execute JavaScript to fill the form
    execute active tab of front window javascript "
        // Wait for page to be ready
        if (document.readyState === 'complete') {
            // Try multiple selectors for email field
            var emailField = document.querySelector('input[type=\"email\"]') || 
                           document.querySelector('input[name=\"email\"]') ||
                           document.querySelector('input[placeholder*=\"email\" i]') ||
                           document.querySelector('#email');
            
            if (emailField) {
                emailField.focus();
                emailField.value = '$email';
                emailField.dispatchEvent(new Event('input', { bubbles: true }));
                console.log('Email field filled: $email');
            } else {
                console.log('Email field not found');
            }
            
            // Try to fill first name if field exists
            var firstNameField = document.querySelector('input[name=\"first_name\"]') ||
                               document.querySelector('input[name=\"firstName\"]') ||
                               document.querySelector('input[placeholder*=\"first\" i]');
            
            if (firstNameField) {
                firstNameField.focus();
                firstNameField.value = '$first_name';
                firstNameField.dispatchEvent(new Event('input', { bubbles: true }));
                console.log('First name field filled: $first_name');
            }
            
            // Try to fill last name if field exists
            var lastNameField = document.querySelector('input[name=\"last_name\"]') ||
                              document.querySelector('input[name=\"lastName\"]') ||
                              document.querySelector('input[placeholder*=\"last\" i]');
            
            if (lastNameField) {
                lastNameField.focus();
                lastNameField.value = '$last_name';
                lastNameField.dispatchEvent(new Event('input', { bubbles: true }));
                console.log('Last name field filled: $last_name');
            }
            
            // Wait a moment then try to submit
            setTimeout(function() {
                var submitButton = document.querySelector('button[type=\"submit\"]') ||
                                 document.querySelector('input[type=\"submit\"]') ||
                                 document.querySelector('button:contains(\"Sign up\")') ||
                                 document.querySelector('button:contains(\"Register\")') ||
                                 document.querySelector('.submit-button');
                
                if (submitButton) {
                    submitButton.click();
                    console.log('Submit button clicked');
                } else {
                    console.log('Submit button not found');
                }
            }, 1000);
        }
    "
end tell
EOF
    
    sleep 5
    log_success "Registration form filled and submitted"
    return 0
}

#------------
# Account Registration Automation (Working Version)
#------------
automate_cursor_registration() {
    local email="$1"
    local password="$2"
    local first_name="$3"
    local last_name="$4"

    step "Starting automated Cursor account registration..."

    # Setup browser automation
    if ! setup_browser_automation; then
        log_error "Failed to setup browser automation"
        return 1
    fi

    # Launch Chrome
    if ! launch_chrome_for_automation; then
        log_error "Failed to launch Chrome"
        return 1
    fi

    # Navigate to registration page
    if ! navigate_to_cursor_page; then
        log_error "Failed to navigate to registration page"
        return 1
    fi

    # Fill and submit registration form
    if ! fill_registration_form "$email" "$first_name" "$last_name"; then
        log_error "Failed to fill registration form"
        return 1
    fi

    # Wait for form submission to process
    sleep 10

    # Check for success or errors
    local page_content=$(osascript << 'EOF'
tell application "Google Chrome"
    execute active tab of front window javascript "document.body.textContent"
end tell
EOF
)

    if echo "$page_content" | grep -qi "verification\|confirm\|check.*email"; then
        log_success "Registration appears successful - verification step detected"
        log_info "Please check the email: $email for verification instructions"
        return 0
    elif echo "$page_content" | grep -qi "error\|invalid\|failed"; then
        log_warn "Registration may have failed - error detected on page"
        log_info "Manual intervention may be required"
        return 1
    else
        log_info "Registration submitted - status unclear"
        log_info "Please check the browser and email: $email"
        return 0
    fi
}

#------------
# Account Database Management (Simplified)
#------------
initialize_account_db() {
    step "Initializing account database..."
    if [ ! -f "$ACCOUNT_DB" ]; then
        echo '{"accounts": [], "last_used": null, "created_count": 0}' > "$ACCOUNT_DB"
        chmod 600 "$ACCOUNT_DB"
        chown "$CURRENT_USER" "$ACCOUNT_DB"
        log_success "Account database initialized"
    else
        log_info "Account database already exists"
    fi
}

save_account_to_db() {
    local email="$1"
    local password="$2"
    local first_name="$3"
    local last_name="$4"
    
    step "Saving account to database..."
    
    # Simple text-based storage as fallback
    local text_db="$HOME/.cursor_accounts.txt"
    echo "=== Account Created: $(date) ===" >> "$text_db"
    echo "Email: $email" >> "$text_db"
    echo "Password: $password" >> "$text_db"
    echo "Name: $first_name $last_name" >> "$text_db"
    echo "================================" >> "$text_db"
    
    chown "$CURRENT_USER" "$text_db"
    chmod 600 "$text_db"
    
    log_success "Account saved to database"
    return 0
}

#------------
# Enhanced Reset Functions (from original script)
#------------
check_root() {
    step "Checking for root privileges..."
    if [ "$EUID" -ne 0 ]; then
        echo -e "${RED}ERROR${NC} This script requires administrator privileges."
        echo "Please run using 'sudo $0'"
        log_error "Not running as root. Exiting."
        exit 1
    fi
    log_info "Privilege check passed (running as root). Operations will affect user: $CURRENT_USER"
}

stop_cursor() {
    step "Stopping Cursor application..."
    local attempt=1
    local max_attempts=5

    while [ $attempt -le $max_attempts ]; do
        local pids
        pids=$(pgrep -if "Cursor.app" || true)

        if [ -z "$pids" ]; then
            log_info "No running Cursor processes found."
            return 0
        fi

        log_warn "Found running Cursor processes (PIDs: $pids)."
        log_warn "Attempting to gracefully shut down Cursor (Attempt $attempt/$max_attempts)..."

        echo "$pids" | xargs kill -TERM 2>/dev/null || true
        sleep 2

        pids=$(pgrep -if "Cursor.app" || true)
        if [ -z "$pids" ]; then
            log_info "Cursor processes closed successfully."
            return 0
        fi

        if [ $attempt -eq $max_attempts ]; then
            log_warn "Graceful shutdown failed, attempting force termination..."
            echo "$pids" | xargs kill -KILL 2>/dev/null || true
            sleep 1

            if pgrep -if "Cursor.app" > /dev/null; then
                log_error "Cursor processes remain even after force termination."
                exit 1
            else
                log_info "Cursor processes have been force closed."
                return 0
            fi
        fi

        ((attempt++))
        sleep 1
    done

    log_error "Failed to close all Cursor processes after $max_attempts attempts."
    exit 1
}

enhanced_clear_storage() {
    step "Enhanced storage clearing..."

    # Clear all Cursor data locations
    local dirs_to_clear=(
        "$CURSOR_SUPPORT/User/state"
        "$CURSOR_SUPPORT/User/History"
        "$CURSOR_SUPPORT/User/workspaceStorage"
        "$CURSOR_SUPPORT/logs"
        "$CURSOR_SUPPORT/CachedData"
        "$CURSOR_SUPPORT/Cache"
        "$CURSOR_SUPPORT/GPUCache"
        "$CURSOR_SUPPORT/Code Cache"
        "$CURSOR_SUPPORT/Partitions/cursor/Cookies"
        "$CURSOR_SUPPORT/Partitions/cursor/Local Storage/leveldb"
        "$CURSOR_SUPPORT/Partitions/cursor/Session Storage"
        "$CURSOR_SUPPORT/Partitions/cursor/IndexedDB"
        "$CURSOR_SUPPORT/Partitions/cursor/Service Worker"
    )

    for dir in "${dirs_to_clear[@]}"; do
        if [ -d "$dir" ]; then
            log_info "Clearing directory: $dir"
            sudo rm -rf "$dir"
            mkdir -p "$dir"
            sudo chown "$CURRENT_USER" "$dir"
            sudo chmod 755 "$dir"
        fi
    done

    # Clear additional locations
    local additional_locations=(
        "$CURRENT_USER_HOME/Library/Logs/Cursor"
        "$CURRENT_USER_HOME/Library/Preferences/com.cursor.Cursor.plist"
        "$CURRENT_USER_HOME/Library/Saved Application State/com.cursor.Cursor.savedState"
        "$CURRENT_USER_HOME/Library/WebKit/com.cursor.Cursor"
    )

    for loc in "${additional_locations[@]}"; do
        if [ -e "$loc" ]; then
            log_info "Clearing: $loc"
            sudo rm -rf "$loc"
        fi
    done

    # Clear temporary files
    find /tmp -name "*cursor*" -o -name "*Cursor*" -type f -exec sudo rm -f {} \; 2>/dev/null || true

    log_success "Enhanced storage clearing completed"
    return 0
}

enhanced_modify_storage() {
    step "Enhanced storage modification..."

    if [ ! -f "$STORAGE_JSON" ]; then
        log_info "No storage.json to modify."
        return 0
    fi

    # Backup existing storage
    local backup_file="$BACKUP_DIR/storage.json.$(date +%Y%m%d_%H%M%S).bak"
    mkdir -p "$BACKUP_DIR"
    sudo cp "$STORAGE_JSON" "$backup_file"
    sudo chown "$CURRENT_USER" "$backup_file"

    # Generate multiple new identifiers for enhanced obfuscation
    local new_uuid=$(generate_enhanced_uuid)
    local new_machine_id=$(openssl rand -hex 32)
    local new_mac_machine_id=$(openssl rand -hex 32)
    local new_device_id=$(generate_enhanced_uuid)
    local new_session_id=$(openssl rand -hex 16)

    log_info "Modifying storage.json with enhanced identifiers..."

    local temp_file=$(mktemp)

    if command -v jq &> /dev/null; then
        jq --arg uuid "$new_uuid" \
           --arg machine_id "$new_machine_id" \
           --arg mac_id "$new_mac_machine_id" \
           --arg device_id "$new_device_id" \
           --arg session_id "$new_session_id" \
           '.["telemetry.devDeviceId"] = $uuid |
            .["telemetry.machineId"] = $machine_id |
            .["telemetry.macMachineId"] = $mac_id |
            .["cursor.deviceId"] = $device_id |
            .["cursor.sessionId"] = $session_id |
            .["cursor.installId"] = $uuid |
            .["cursor.firstInstall"] = (now | todate)' \
           "$STORAGE_JSON" > "$temp_file"
    else
        # Fallback to sed
        cp "$STORAGE_JSON" "$temp_file"
        sed -i.bak "s/\"telemetry.devDeviceId\": \"[^\"]*\"/\"telemetry.devDeviceId\": \"$new_uuid\"/" "$temp_file"
        sed -i.bak "s/\"telemetry.machineId\": \"[^\"]*\"/\"telemetry.machineId\": \"$new_machine_id\"/" "$temp_file"
        sed -i.bak "s/\"telemetry.macMachineId\": \"[^\"]*\"/\"telemetry.macMachineId\": \"$new_mac_machine_id\"/" "$temp_file"
        rm -f "${temp_file}.bak"
    fi

    # Verify and replace
    if [ -s "$temp_file" ]; then
        sudo cp "$temp_file" "$STORAGE_JSON"
        sudo chown "$CURRENT_USER" "$STORAGE_JSON"
        sudo chmod 644 "$STORAGE_JSON"
        rm -f "$temp_file"
        log_success "Enhanced storage modification completed"
    else
        log_error "Failed to modify storage.json"
        rm -f "$temp_file"
        return 1
    fi

    return 0
}

#------------
# Main Workflow Functions
#------------
create_new_account() {
    step "Creating new Cursor account..."

    # Generate credentials
    local credentials=$(generate_random_credentials)
    IFS=':' read -r first_name last_name random_num <<< "$credentials"

    # Get temporary email
    local email=$(get_temporary_email)
    local password=$(generate_secure_password)

    log_info "Generated credentials:"
    log_info "  Name: $first_name $last_name"
    log_info "  Email: $email"
    log_info "  Password: [HIDDEN]"

    # Attempt registration
    if automate_cursor_registration "$email" "$password" "$first_name" "$last_name"; then
        # Save to database
        save_account_to_db "$email" "$password" "$first_name" "$last_name"
        log_success "New account created and saved successfully!"

        # Display account info for user
        echo
        echo -e "${CYAN}=== NEW ACCOUNT CREATED ===${NC}"
        echo -e "${GREEN}Email:${NC} $email"
        echo -e "${GREEN}Password:${NC} $password"
        echo -e "${GREEN}Name:${NC} $first_name $last_name"
        echo -e "${CYAN}=========================${NC}"
        echo
        echo -e "${YELLOW}Please check your email for verification and complete the setup manually if needed.${NC}"

        return 0
    else
        log_error "Failed to create new account"
        return 1
    fi
}

enhanced_reset_with_new_account() {
    step "Performing enhanced reset with new account creation..."

    # Stop Cursor
    stop_cursor

    # Clear storage
    enhanced_clear_storage

    # Modify storage with new identifiers
    enhanced_modify_storage

    # Create new account
    if create_new_account; then
        log_success "Enhanced reset with new account completed successfully!"
        log_info "You can now restart Cursor and use the new account."
        return 0
    else
        log_warn "Account creation failed, but reset was completed."
        log_info "You may need to manually create an account."
        return 1
    fi
}

show_account_status() {
    step "Showing account database status..."

    local text_db="$HOME/.cursor_accounts.txt"
    if [ -f "$text_db" ]; then
        local account_count=$(grep -c "Email:" "$text_db" 2>/dev/null || echo "0")
        echo -e "\n${CYAN}=== Account Database Status ===${NC}"
        echo -e "${GREEN}Total accounts created: ${WHITE}$account_count${NC}"
        echo -e "${CYAN}================================${NC}\n"

        if [ "$account_count" -gt 0 ]; then
            echo -e "${GREEN}Recent accounts:${NC}"
            tail -20 "$text_db" | grep -E "(Email:|Password:|Name:)" | tail -9
        else
            echo -e "${YELLOW}No accounts found in database.${NC}"
        fi
    else
        echo -e "${YELLOW}No account database found.${NC}"
    fi

    return 0
}

#------------
# Menu System
#------------
show_enhanced_menu() {
    echo
    echo -e "${CYAN}========================================${NC}"
    echo -e "${WHITE}   Enhanced Cursor Trial Reset Tool    ${NC}"
    echo -e "${PURPLE}           Version $SCRIPT_VERSION (Fixed)     ${NC}"
    echo -e "${CYAN}========================================${NC}"
    echo
    echo -e "${GREEN}1)${NC} Enhanced reset with new account creation"
    echo -e "${GREEN}2)${NC} Create new account only"
    echo -e "${GREEN}3)${NC} Show account database status"
    echo -e "${GREEN}4)${NC} Basic reset (original functionality)"
    echo -e "${GREEN}5)${NC} Initialize account database"
    echo -e "${GREEN}6)${NC} Exit"
    echo
    echo -n "Enter your choice [1-6]: "
    read -r choice

    case $choice in
        1)
            check_root
            initialize_account_db
            enhanced_reset_with_new_account
            ;;
        2)
            initialize_account_db
            create_new_account
            ;;
        3)
            show_account_status
            ;;
        4)
            check_root
            stop_cursor
            enhanced_clear_storage
            enhanced_modify_storage
            log_success "Basic reset completed!"
            ;;
        5)
            initialize_account_db
            ;;
        6)
            log_info "Exiting Enhanced Cursor Reset Tool."
            exit 0
            ;;
        *)
            log_error "Invalid choice. Please try again."
            show_enhanced_menu
            ;;
    esac
}

#------------
# Main execution
#------------
main() {
    initialize_log

    # Check environment
    if ! command -v osascript >/dev/null; then
        log_error "osascript not found. This script requires macOS."
        exit 1
    fi

    # Show enhanced menu
    show_enhanced_menu
}

main
