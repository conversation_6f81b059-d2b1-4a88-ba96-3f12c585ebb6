#!/usr/bin/env bash

# macOS Cursor App Reset and Modification Script (Updated)
#
# IMPORTANT:
# - This script modifies internal files of the Cursor application to reset device identification logic.
# - Altering the app may violate Cursor's terms of service. Use at your own risk.
# - Success depends on the Cursor version; updates may break this script.
# - Always back up important data before running.
# - Consider supporting the developer by purchasing a valid license.
#
# This script only runs on macOS (Darwin).

# Exit on errors, undefined vars, and pipe failures
set -euo pipefail

# Check macOS
if [[ "$(uname)" != "Darwin" ]]; then
    echo "Error: macOS required. Exiting." >&2
    exit 1
fi

# Steps counter
STEP=0
step() { ((STEP++)); echo -e "[Step $STEP] $1"; log_info "$1"; }

echo "Starting Cursor App reset & modification..."

#------------
# Logging Setup
#------------
LOG_DIR="$HOME/Library/Logs"
mkdir -p "$LOG_DIR"
LOG_FILE="$LOG_DIR/cursor_app_modifier.log"

initialize_log() {
    echo "========== Cursor Modifier Log Start: $(date) ==========" > "$LOG_FILE"
    chmod 644 "$LOG_FILE"
    if [ "$EUID" -eq 0 ] && [ -n "${SUDO_USER:-}" ]; then
        chown "$SUDO_USER" "$LOG_FILE"
    fi
}

_log() {
    local level="$1" msg="$2" timestamp
    timestamp="$(date '+%Y-%m-%d %H:%M:%S')"
    echo "[$level] $timestamp $msg" >> "$LOG_FILE"
}

log_info()  { _log "INFO"  "$1"; }
log_warn()  { _log "WARN"  "$1"; }
log_error() { _log "ERROR" "$1"; }

#------------
# Helpers
#------------
get_current_user() {
    if [ "$EUID" -eq 0 ] && [ -n "${SUDO_USER:-}" ]; then
        echo "$SUDO_USER"
    else
        echo "$USER"
    fi
}

CURRENT_USER=$(get_current_user)
CURRENT_USER_HOME=$(eval echo "~$CURRENT_USER")

# Paths
CURSOR_SUPPORT="$CURRENT_USER_HOME/Library/Application Support/Cursor"
STORAGE_JSON="$CURSOR_SUPPORT/User/globalStorage/storage.json"
BACKUP_DIR="$CURSOR_SUPPORT/User/globalStorage/backups"
CURSOR_APP="/Applications/Cursor.app"

#------------
# Ensure running as root
#------------
check_root() {
    step "Checking for root privileges..."
    if [ "$EUID" -ne 0 ]; then
        echo "Please run as root (sudo)." >&2
        log_error "Not running as root. Exiting."
        exit 1
    fi
    echo "Privilege check passed."
}

#------------
# Stop Cursor process
#------------
stop_cursor() {
    step "Stopping Cursor application..."
    local pids
    pids=$(pgrep -if "Cursor.app" || true)
    if [ -z "$pids" ]; then
        echo "No running Cursor instances."
        return
    fi
    echo "Found PIDs: $pids"
    for sig in TERM KILL; do
        echo "Sending $sig..."
        echo "$pids" | xargs kill -$sig 2>/dev/null || true
        sleep 2
        pids=$(pgrep -if "Cursor.app" || true)
        [ -z "$pids" ] && { echo "Cursor stopped."; return; }
    done
    echo "Failed to stop Cursor. Please close it manually." >&2
    exit 1
}

#------------
# Backup storage.json
#------------
backup_storage() {
    step "Backing up storage.json..."
    if [ ! -f "$STORAGE_JSON" ]; then
        echo "No storage.json to back up."
        return
    fi
    mkdir -p "$BACKUP_DIR"
    local dest="$BACKUP_DIR/storage.json.$(date +%Y%m%d_%H%M%S).bak"
    cp "$STORAGE_JSON" "$dest"
    echo "Backed up to $dest"
}

#------------
# Process storage (only backup)
#------------
process_storage() {
    step "Processing storage configuration..."
    backup_storage
}

#------------
# Modify application JS files
#------------
modify_app() {
    step "Patching Cursor.app files..."
    if [ ! -d "$CURSOR_APP" ]; then
        log_error "Cursor.app not found."
        echo "Cursor.app missing at $CURSOR_APP" >&2
        exit 1
    fi

    local targets=(
        "Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js"
        "Contents/Resources/app/out/main.js"
        "Contents/Resources/app/out/vs/code/node/cliProcessMain.js"
    )
    local stamp=$(date +%Y%m%d_%H%M%S)
    local bak_dir="$CURSOR_SUPPORT/backups/app_$stamp"
    mkdir -p "$bak_dir"
    cp -a "$CURSOR_APP" "$bak_dir/"
    echo "Backup created at $bak_dir"

    local tmpdir="/tmp/cursor_mod_$stamp"
    mkdir -p "$tmpdir"
    cp -a "$CURSOR_APP" "$tmpdir/Cursor.app"
    chmod -R u+w "$tmpdir/Cursor.app"
    codesign --remove-signature "$tmpdir/Cursor.app" || true

    local patched=false
    for frel in "${targets[@]}"; do
        local f="$tmpdir/Cursor.app/$frel"
        if [ -f "$f" ]; then
            sed -i '.orig' 's/IOPlatformUUID/crypto.randomUUID()/g' "$f" && patched=true && echo "Patched $frel"
        fi
    done
    if ! $patched; then
        echo "No JS patches applied; check compatibility." >&2
        exit 1
    fi

    step "Re-signing and installing patched app..."
    codesign --sign - --force --deep --preserve-metadata=identifier,entitlements,flags "$tmpdir/Cursor.app"
    rm -rf "$CURSOR_APP"
    mv "$tmpdir/Cursor.app" "$CURSOR_APP"
    chown -R root:wheel "$CURSOR_APP"
    chmod -R 755 "$CURSOR_APP"
    echo "Patch installation complete."
}

#------------
# Disable auto-update
#------------
disable_update() {
    step "Disabling auto-update..."
    local yml="$CURSOR_APP/Contents/Resources/app-update.yml"
    if [ -f "$yml" ]; then
        cp "$yml" "$yml.bak"
        chown root:wheel "$yml"
        chmod 444 "$yml"
        echo "Disabled via $yml permissions."
    else
        echo "No update config found; skipping."
    fi
    local cache="$CURRENT_USER_HOME/Library/Caches/cursor-updater"
    mkdir -p "$cache"
    chown root:wheel "$cache"
    chmod 555 "$cache"
    echo "Cache directory set read-only."
}

#------------
# Main execution
#------------
main() {
    initialize_log
    echo "Log initialized at $LOG_FILE"
    step "Verifying environment..."
    command -v codesign >/dev/null || { echo "codesign missing; install Xcode tools." >&2; exit 1; }

    check_root
    stop_cursor
    process_storage
    modify_app
    disable_update

    echo "All steps completed in $STEP steps." 
}

main
