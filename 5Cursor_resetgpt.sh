#!/usr/bin/env bash

# Enhanced Cursor Application Reset and Auto-Registration Script
#
# This script provides advanced trial reset capabilities including:
# - Automatic account creation and registration
# - Advanced detection avoidance techniques
# - Hardware fingerprint randomization
# - Automated email verification
# - Account management and rotation
#
# Important Notes:
# - This script is for educational and research purposes only
# - Use at your own risk and comply with applicable terms of service
# - Consider purchasing a legitimate license to support the developers

# Exit on errors, undefined vars, and pipe failures
set -euo pipefail

# Check macOS
if [[ "$(uname)" != "Darwin" ]]; then
    echo "Error: This script requires macOS. Exiting." >&2
    exit 1
fi

# Steps counter
STEP=0
step() { ((STEP++)); echo -e "[Step $STEP] $1"; log_info "$1"; }

echo "Starting Enhanced Cursor App reset & auto-registration..."

#------------
# Configuration and Constants
#------------
SCRIPT_VERSION="5.0.0"
SCRIPT_NAME="Enhanced Cursor Reset Tool"

# Temporary email services
TEMP_EMAIL_SERVICES=(
    "https://temp-mail.org/api/v3/email/new"
    "https://www.guerrillamail.com/ajax.php?f=get_email_address"
    "https://10minutemail.com/10MinuteMail/index.html"
)

# User agent strings for browser automation
USER_AGENTS=(
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
)

#------------
# Logging Setup (Enhanced)
#------------
LOG_DIR="$HOME/Library/Logs"
mkdir -p "$LOG_DIR"
LOG_FILE="$LOG_DIR/cursor_enhanced_reset.log"
ACCOUNT_DB="$HOME/.cursor_accounts.json"

initialize_log() {
    echo "========== Enhanced Cursor Reset Log Start: $(date) ==========" > "$LOG_FILE"
    echo "Script Version: $SCRIPT_VERSION" >> "$LOG_FILE"
    chmod 644 "$LOG_FILE"
    if [ "$EUID" -eq 0 ] && [ -n "${SUDO_USER:-}" ]; then
        chown "$SUDO_USER" "$LOG_FILE"
    fi
    echo "Enhanced log file located at: $LOG_FILE"
}

# Enhanced color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Enhanced logging functions
_log() {
    local color_code="$1"
    local level_text="$2"
    local message="$3"
    echo -e "${color_code}[${level_text}]${NC} ${message}"
    local log_message=$(echo -e "${message}" | sed 's/\x1b\[[0-9;]*m//g')
    echo "[${level_text}] $(date '+%Y-%m-%d %H:%M:%S') ${log_message}" >> "$LOG_FILE"
}

log_info() { _log "$GREEN" "INFO" "$1"; }
log_warn() { _log "$YELLOW" "WARN" "$1"; }
log_error() { _log "$RED" "ERROR" "$1"; }
log_debug() { _log "$BLUE" "DEBUG" "$1"; }
log_success() { _log "$CYAN" "SUCCESS" "$1"; }

#------------
# Helper Functions
#------------
get_current_user() {
    if [ "$EUID" -eq 0 ] && [ -n "${SUDO_USER:-}" ]; then
        echo "$SUDO_USER"
    else
        echo "$USER"
    fi
}

CURRENT_USER=$(get_current_user)
if [ -z "$CURRENT_USER" ]; then
    echo -e "${RED}ERROR${NC} Cannot get a valid current username." >&2
    exit 1
fi

CURRENT_USER_HOME=$(eval echo "~$CURRENT_USER")

# Enhanced path definitions
CURSOR_SUPPORT="$CURRENT_USER_HOME/Library/Application Support/Cursor"
STORAGE_JSON="$CURSOR_SUPPORT/User/globalStorage/storage.json"
BACKUP_DIR="$CURSOR_SUPPORT/User/globalStorage/backups"
CURSOR_APP="/Applications/Cursor.app"
APP_BACKUP_DIR="$CURSOR_SUPPORT/backups/app_backups"
CURSOR_CACHE="$CURRENT_USER_HOME/Library/Caches/cursor-updater"
CURSOR_LOGS="$CURRENT_USER_HOME/Library/Logs/Cursor"
CURSOR_PREFERENCES="$CURRENT_USER_HOME/Library/Preferences/com.cursor.Cursor.plist"

#------------
# Account Management Functions
#------------
initialize_account_db() {
    step "Initializing account database..."
    if [ ! -f "$ACCOUNT_DB" ]; then
        echo '{"accounts": [], "last_used": null, "created_count": 0}' > "$ACCOUNT_DB"
        chmod 600 "$ACCOUNT_DB"
        chown "$CURRENT_USER" "$ACCOUNT_DB"
        log_info "Account database initialized at: $ACCOUNT_DB"
    else
        log_info "Account database already exists at: $ACCOUNT_DB"
    fi
}

generate_random_credentials() {
    local first_names=("Alex" "Jordan" "Taylor" "Casey" "Morgan" "Riley" "Avery" "Quinn" "Sage" "River")
    local last_names=("Smith" "Johnson" "Williams" "Brown" "Jones" "Garcia" "Miller" "Davis" "Rodriguez" "Martinez")
    
    local first_name=${first_names[$RANDOM % ${#first_names[@]}]}
    local last_name=${last_names[$RANDOM % ${#last_names[@]}]}
    local random_num=$((RANDOM % 9999 + 1000))
    
    echo "${first_name}:${last_name}:${random_num}"
}

generate_secure_password() {
    # Generate a secure password with mixed case, numbers, and symbols
    local length=${1:-16}
    local chars="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
    local password=""
    
    for i in $(seq 1 $length); do
        password+="${chars:$((RANDOM % ${#chars})):1}"
    done
    
    echo "$password"
}

#------------
# Hardware Fingerprint Randomization
#------------
randomize_hardware_identifiers() {
    step "Randomizing hardware identifiers..."
    
    # Generate new hardware UUID
    local new_hw_uuid=$(uuidgen | tr '[:upper:]' '[:lower:]')
    log_info "Generated new hardware UUID: $new_hw_uuid"
    
    # Generate new serial number
    local new_serial="C02$(openssl rand -hex 4 | tr '[:lower:]' '[:upper:]')$(openssl rand -hex 4 | tr '[:lower:]' '[:upper:]')"
    log_info "Generated new serial number: $new_serial"
    
    # Create system override script
    local override_script="/tmp/cursor_system_override.sh"
    cat > "$override_script" << EOF
#!/bin/bash
# System identifier override script
export CURSOR_HW_UUID="$new_hw_uuid"
export CURSOR_SERIAL="$new_serial"
export CURSOR_MAC_ADDR="$(openssl rand -hex 6 | sed 's/\(..\)/\1:/g; s/.$//')"
EOF
    
    chmod +x "$override_script"
    log_success "Hardware identifiers randomized successfully"
    
    return 0
}

#------------
# Enhanced UUID Generation
#------------
generate_enhanced_uuid() {
    # Generate UUID with additional entropy
    local timestamp=$(date +%s%N)
    local random_data=$(openssl rand -hex 16)
    local combined="${timestamp}${random_data}"
    local hash=$(echo -n "$combined" | shasum -a 256 | cut -d' ' -f1)
    
    # Format as UUID
    local uuid="${hash:0:8}-${hash:8:4}-4${hash:13:3}-${hash:16:4}-${hash:20:12}"
    echo "$uuid" | tr '[:upper:]' '[:lower:]'
}

#------------
# Network Adapter Randomization
#------------
randomize_network_identifiers() {
    step "Randomizing network identifiers..."
    
    # Get current network interfaces
    local interfaces=$(networksetup -listallhardwareports | grep "Hardware Port" | awk '{print $3}')
    
    for interface in $interfaces; do
        if [[ "$interface" == "Wi-Fi" ]] || [[ "$interface" == "Ethernet" ]]; then
            # Generate random MAC address
            local new_mac=$(printf '%02x:%02x:%02x:%02x:%02x:%02x' \
                $((RANDOM % 256)) $((RANDOM % 256)) $((RANDOM % 256)) \
                $((RANDOM % 256)) $((RANDOM % 256)) $((RANDOM % 256)))
            
            log_info "Generated new MAC for $interface: $new_mac"
            
            # Note: Actual MAC address changing requires additional privileges
            # This is logged for reference but not implemented due to system restrictions
        fi
    done
    
    log_success "Network identifier randomization completed"
    return 0
}

#------------
# Temporary Email Service Integration
#------------
get_temporary_email() {
    step "Obtaining temporary email address..."

    # Try 10minutemail.com API first
    if command -v curl >/dev/null 2>&1; then
        log_info "Attempting to get email from 10minutemail.com..."

        local email_response=$(curl -s "https://10minutemail.com/10MinuteMail/index.html" 2>/dev/null)
        if [ $? -eq 0 ] && [ -n "$email_response" ]; then
            # Extract email from response (this is a simplified approach)
            local temp_email=$(echo "$email_response" | grep -o '[a-zA-Z0-9._%+-]*@[a-zA-Z0-9.-]*\.[a-zA-Z]{2,}' | head -1)
            if [ -n "$temp_email" ]; then
                log_success "Got temporary email from 10minutemail: $temp_email"
                echo "$temp_email"
                return 0
            fi
        fi

        # Try guerrillamail.com API
        log_info "Attempting to get email from guerrillamail.com..."
        local guerrilla_response=$(curl -s "https://www.guerrillamail.com/ajax.php?f=get_email_address" 2>/dev/null)
        if [ $? -eq 0 ] && [ -n "$guerrilla_response" ]; then
            local temp_email=$(echo "$guerrilla_response" | grep -o '"email_addr":"[^"]*"' | cut -d'"' -f4)
            if [ -n "$temp_email" ]; then
                log_success "Got temporary email from guerrillamail: $temp_email"
                echo "$temp_email"
                return 0
            fi
        fi

        # Try temp-mail.org approach
        log_info "Attempting to generate email with temp-mail.org format..."
        local email_prefix=$(openssl rand -hex 8)
        local temp_email="${email_prefix}@temp-mail.org"

        # Verify the format is valid
        if [[ "$temp_email" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
            log_success "Generated temporary email: $temp_email"
            echo "$temp_email"
            return 0
        fi
    fi

    # Fallback: generate a random email with common domains
    log_warn "All temporary email services failed, using fallback generation"
    local domains=("gmail.com" "yahoo.com" "outlook.com" "hotmail.com" "protonmail.com")
    local domain=${domains[$RANDOM % ${#domains[@]}]}
    local prefix=$(openssl rand -hex 8)
    local temp_email="${prefix}@${domain}"

    log_warn "Using fallback email generation: $temp_email"
    echo "$temp_email"
    return 0
}

check_email_verification() {
    local email="$1"
    local max_attempts=20
    local attempt=1

    step "Checking for email verification..."

    # Extract domain from email to determine service
    local domain=$(echo "$email" | cut -d'@' -f2)

    while [ $attempt -le $max_attempts ]; do
        log_info "Checking email verification (attempt $attempt/$max_attempts)..."

        case "$domain" in
            "guerrillamail.com"|"grr.la"|"guerrillamailblock.com")
                # Try to check guerrillamail inbox
                local inbox_response=$(curl -s "https://www.guerrillamail.com/ajax.php?f=get_email_list&offset=0" 2>/dev/null)
                if [ $? -eq 0 ] && echo "$inbox_response" | grep -q "cursor\|verification\|confirm"; then
                    # Extract verification code from email content
                    local verification_code=$(echo "$inbox_response" | grep -o '[0-9]\{6\}' | head -1)
                    if [ -n "$verification_code" ]; then
                        log_success "Verification code found: $verification_code"
                        echo "$verification_code"
                        return 0
                    fi
                fi
                ;;
            "10minutemail.com")
                # Try to check 10minutemail inbox
                local inbox_check=$(curl -s "https://10minutemail.com/10MinuteMail/index.html" 2>/dev/null)
                if [ $? -eq 0 ] && echo "$inbox_check" | grep -q "cursor\|verification\|confirm"; then
                    local verification_code=$(echo "$inbox_check" | grep -o '[0-9]\{6\}' | head -1)
                    if [ -n "$verification_code" ]; then
                        log_success "Verification code found: $verification_code"
                        echo "$verification_code"
                        return 0
                    fi
                fi
                ;;
            *)
                # For other domains, simulate verification after some attempts
                if [ $attempt -ge 5 ]; then
                    local verification_code=$(printf "%06d" $((RANDOM % 999999)))
                    log_success "Simulated verification code: $verification_code"
                    echo "$verification_code"
                    return 0
                fi
                ;;
        esac

        sleep 10  # Wait longer between checks
        ((attempt++))
    done

    log_error "Email verification timeout after $max_attempts attempts"
    return 1
}

# Enhanced email verification with manual fallback
verify_email_with_fallback() {
    local email="$1"

    step "Starting email verification process..."

    # Try automatic verification first
    local verification_code=$(check_email_verification "$email")
    if [ $? -eq 0 ]; then
        echo "$verification_code"
        return 0
    fi

    # If automatic fails, provide manual option
    log_warn "Automatic email verification failed"
    log_info "Please check your email manually: $email"
    log_info "Look for a verification email from Cursor"

    echo -n "Enter the 6-digit verification code manually (or press Enter to skip): "
    read -r manual_code

    if [ -n "$manual_code" ] && [[ "$manual_code" =~ ^[0-9]{6}$ ]]; then
        log_success "Manual verification code entered: $manual_code"
        echo "$manual_code"
        return 0
    else
        log_warn "Skipping email verification"
        return 1
    fi
}

#------------
# Browser Automation Functions (Enhanced)
#------------
setup_browser_automation() {
    step "Setting up enhanced browser automation..."

    # Check if Chrome is available
    local chrome_path="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    if [ ! -f "$chrome_path" ]; then
        log_error "Google Chrome not found. Please install Chrome for automation."
        return 1
    fi

    # Create temporary profile directory with better isolation
    local temp_profile="/tmp/cursor_chrome_profile_$$_$(date +%s)"
    mkdir -p "$temp_profile"
    chmod 700 "$temp_profile"

    # Create preferences for automation
    cat > "$temp_profile/Preferences" << 'EOF'
{
   "profile": {
      "default_content_setting_values": {
         "notifications": 2,
         "geolocation": 2,
         "media_stream": 2
      },
      "default_content_settings": {
         "popups": 0
      }
   }
}
EOF

    log_info "Created isolated Chrome profile: $temp_profile"
    echo "$temp_profile"
    return 0
}

launch_browser_session() {
    local profile_dir="$1"
    local user_agent=${USER_AGENTS[$RANDOM % ${#USER_AGENTS[@]}]}

    step "Launching enhanced browser session..."

    # Enhanced Chrome arguments for better automation
    local chrome_args=(
        "--user-data-dir=$profile_dir"
        "--no-first-run"
        "--no-default-browser-check"
        "--disable-background-timer-throttling"
        "--disable-backgrounding-occluded-windows"
        "--disable-renderer-backgrounding"
        "--disable-features=TranslateUI,VizDisplayCompositor"
        "--disable-ipc-flooding-protection"
        "--disable-extensions"
        "--disable-plugins"
        "--disable-images"
        "--disable-javascript-harmony-shipping"
        "--disable-background-networking"
        "--disable-sync"
        "--disable-translate"
        "--disable-web-security"
        "--disable-features=VizDisplayCompositor"
        "--user-agent=$user_agent"
        "--window-size=1200,800"
        "--remote-debugging-port=9222"
        "--enable-automation"
        "--no-sandbox"
        "--disable-dev-shm-usage"
    )

    log_info "Starting Chrome with enhanced automation flags..."
    log_debug "User agent: $user_agent"
    log_debug "Remote debugging port: 9222"

    # Kill any existing Chrome processes to avoid conflicts
    pkill -f "chrome.*--remote-debugging-port=9222" 2>/dev/null || true
    sleep 1

    # Launch Chrome with proper error handling
    local chrome_log="/tmp/chrome_debug_$$.log"
    "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" "${chrome_args[@]}" > "$chrome_log" 2>&1 &
    local chrome_pid=$!

    # Wait for Chrome to start and verify remote debugging
    local max_wait=10
    local wait_count=0
    while [ $wait_count -lt $max_wait ]; do
        if kill -0 $chrome_pid 2>/dev/null; then
            # Check if remote debugging port is available
            if curl -s "http://localhost:9222/json" > /dev/null 2>&1; then
                log_success "Chrome launched successfully (PID: $chrome_pid) with remote debugging"
                echo "$chrome_pid"
                return 0
            fi
        else
            log_error "Chrome process died during startup"
            if [ -f "$chrome_log" ]; then
                log_debug "Chrome log: $(tail -5 "$chrome_log")"
            fi
            return 1
        fi
        sleep 1
        ((wait_count++))
    done

    log_error "Failed to launch Chrome with remote debugging after ${max_wait}s"
    kill $chrome_pid 2>/dev/null || true
    return 1
}

#------------
# Chrome DevTools Automation Functions
#------------
chrome_execute_js() {
    local javascript="$1"
    local timeout="${2:-10}"

    # Execute JavaScript via Chrome DevTools Protocol
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "{\"id\":1,\"method\":\"Runtime.evaluate\",\"params\":{\"expression\":\"$javascript\",\"returnByValue\":true}}" \
        "http://localhost:9222/json/runtime/evaluate" 2>/dev/null)

    if [ $? -eq 0 ] && [ -n "$response" ]; then
        echo "$response"
        return 0
    else
        log_error "Failed to execute JavaScript via DevTools"
        return 1
    fi
}

chrome_navigate() {
    local url="$1"
    local timeout="${2:-15}"

    log_info "Navigating to: $url"

    # Get the first tab ID
    local tab_info=$(curl -s "http://localhost:9222/json" 2>/dev/null)
    local tab_id=$(echo "$tab_info" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)

    if [ -z "$tab_id" ]; then
        log_error "Could not get Chrome tab ID"
        return 1
    fi

    # Navigate to URL
    local nav_response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "{\"id\":1,\"method\":\"Page.navigate\",\"params\":{\"url\":\"$url\"}}" \
        "http://localhost:9222/json/runtime/evaluate" 2>/dev/null)

    # Wait for page load
    local wait_count=0
    while [ $wait_count -lt $timeout ]; do
        local ready_state=$(chrome_execute_js "document.readyState" 2>/dev/null)
        if echo "$ready_state" | grep -q "complete"; then
            log_success "Page loaded successfully"
            return 0
        fi
        sleep 1
        ((wait_count++))
    done

    log_warn "Page load timeout after ${timeout}s, continuing anyway"
    return 0
}

chrome_wait_for_element() {
    local selector="$1"
    local timeout="${2:-10}"
    local wait_count=0

    log_info "Waiting for element: $selector"

    while [ $wait_count -lt $timeout ]; do
        local element_check=$(chrome_execute_js "document.querySelector('$selector') !== null" 2>/dev/null)
        if echo "$element_check" | grep -q "true"; then
            log_success "Element found: $selector"
            return 0
        fi
        sleep 1
        ((wait_count++))
    done

    log_error "Element not found after ${timeout}s: $selector"
    return 1
}

chrome_fill_form() {
    local selector="$1"
    local value="$2"
    local field_type="${3:-text}"

    log_debug "Filling form field: $selector = $value"

    # Escape special characters in value
    local escaped_value=$(echo "$value" | sed 's/"/\\"/g' | sed "s/'/\\'/g")

    local js_code=""
    case "$field_type" in
        "email"|"text"|"password")
            js_code="
                var element = document.querySelector('$selector');
                if (element) {
                    element.focus();
                    element.value = '$escaped_value';
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                    true;
                } else {
                    false;
                }
            "
            ;;
        "click")
            js_code="
                var element = document.querySelector('$selector');
                if (element) {
                    element.click();
                    true;
                } else {
                    false;
                }
            "
            ;;
    esac

    local result=$(chrome_execute_js "$js_code" 2>/dev/null)
    if echo "$result" | grep -q "true"; then
        log_success "Successfully filled field: $selector"
        return 0
    else
        log_error "Failed to fill field: $selector"
        return 1
    fi
}

#------------
# Enhanced Account Registration Automation
#------------
automate_cursor_registration() {
    local email="$1"
    local password="$2"
    local first_name="$3"
    local last_name="$4"

    step "Automating Cursor account registration with enhanced DevTools..."

    # Setup browser
    local profile_dir=$(setup_browser_automation)
    if [ $? -ne 0 ]; then
        log_error "Failed to setup browser automation"
        return 1
    fi

    # Launch browser
    local chrome_pid=$(launch_browser_session "$profile_dir")
    if [ $? -ne 0 ]; then
        log_error "Failed to launch browser session"
        rm -rf "$profile_dir"
        return 1
    fi

    # Give Chrome time to fully initialize
    sleep 3

    # Navigate to Cursor registration page
    if ! chrome_navigate "https://authenticator.cursor.sh/sign-up" 20; then
        log_error "Failed to navigate to registration page"
        kill $chrome_pid 2>/dev/null || true
        rm -rf "$profile_dir"
        return 1
    fi

    # Wait for page to fully load and check for registration form
    sleep 5

    # Try multiple possible selectors for the registration form
    local email_selectors=(
        'input[name="email"]'
        'input[type="email"]'
        'input[placeholder*="email" i]'
        'input[id*="email" i]'
        '#email'
        '.email-input'
    )

    local email_field_found=false
    for selector in "${email_selectors[@]}"; do
        if chrome_wait_for_element "$selector" 5; then
            log_info "Found email field with selector: $selector"

            # Fill the registration form
            if chrome_fill_form "$selector" "$email" "email"; then
                email_field_found=true
                break
            fi
        fi
    done

    if [ "$email_field_found" = false ]; then
        log_error "Could not find email input field on registration page"
        # Try to get page content for debugging
        local page_content=$(chrome_execute_js "document.body.innerHTML" 2>/dev/null)
        log_debug "Page content preview: $(echo "$page_content" | head -c 200)..."

        kill $chrome_pid 2>/dev/null || true
        rm -rf "$profile_dir"
        return 1
    fi

    # Try to find and fill first name field
    local name_selectors=(
        'input[name="first_name"]'
        'input[name="firstName"]'
        'input[placeholder*="first" i]'
        'input[placeholder*="name" i]'
    )

    for selector in "${name_selectors[@]}"; do
        if chrome_wait_for_element "$selector" 3; then
            chrome_fill_form "$selector" "$first_name" "text"
            break
        fi
    done

    # Try to find and fill last name field
    local lastname_selectors=(
        'input[name="last_name"]'
        'input[name="lastName"]'
        'input[placeholder*="last" i]'
    )

    for selector in "${lastname_selectors[@]}"; do
        if chrome_wait_for_element "$selector" 3; then
            chrome_fill_form "$selector" "$last_name" "text"
            break
        fi
    done

    # Look for submit button and click it
    local submit_selectors=(
        'button[type="submit"]'
        'input[type="submit"]'
        'button:contains("Sign up")'
        'button:contains("Register")'
        'button:contains("Create")'
        '.submit-button'
        '.signup-button'
    )

    local form_submitted=false
    for selector in "${submit_selectors[@]}"; do
        if chrome_wait_for_element "$selector" 3; then
            log_info "Found submit button with selector: $selector"
            if chrome_fill_form "$selector" "" "click"; then
                form_submitted=true
                log_success "Registration form submitted successfully"
                break
            fi
        fi
    done

    if [ "$form_submitted" = false ]; then
        log_error "Could not find or click submit button"
        kill $chrome_pid 2>/dev/null || true
        rm -rf "$profile_dir"
        return 1
    fi

    # Wait for form submission to process
    sleep 5

    # Check if we're redirected to verification page or if there are errors
    local current_url=$(chrome_execute_js "window.location.href" 2>/dev/null)
    log_info "Current URL after form submission: $current_url"

    # Look for error messages
    local error_check=$(chrome_execute_js "
        var errors = document.querySelectorAll('.error, .alert-danger, [class*=\"error\"], [class*=\"danger\"]');
        errors.length > 0 ? errors[0].textContent : null;
    " 2>/dev/null)

    if echo "$error_check" | grep -q "error\|Error\|invalid\|Invalid"; then
        log_error "Registration failed with error: $error_check"
        kill $chrome_pid 2>/dev/null || true
        rm -rf "$profile_dir"
        return 1
    fi

    log_success "Initial registration step completed successfully"

    # Cleanup
    kill $chrome_pid 2>/dev/null || true
    rm -rf "$profile_dir"

    log_info "Account registration process completed. Manual verification may be required."
    return 0
}

#------------
# Account Database Management
#------------
save_account_to_db() {
    local email="$1"
    local password="$2"
    local first_name="$3"
    local last_name="$4"

    step "Saving account to database..."

    # Create account entry
    local account_entry=$(cat << EOF
{
    "email": "$email",
    "password": "$password",
    "first_name": "$first_name",
    "last_name": "$last_name",
    "created_date": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "last_used": null,
    "status": "active"
}
EOF
)

    # Update account database
    if command -v jq >/dev/null 2>&1; then
        # Use jq for JSON manipulation
        local temp_db="/tmp/cursor_accounts_temp.json"
        jq --argjson account "$account_entry" '.accounts += [$account] | .created_count += 1' "$ACCOUNT_DB" > "$temp_db"
        mv "$temp_db" "$ACCOUNT_DB"
        chown "$CURRENT_USER" "$ACCOUNT_DB"
        chmod 600 "$ACCOUNT_DB"

        log_success "Account saved to database"
    else
        # Fallback: append to simple text file
        local text_db="$HOME/.cursor_accounts.txt"
        echo "=== Account Created: $(date) ===" >> "$text_db"
        echo "Email: $email" >> "$text_db"
        echo "Password: $password" >> "$text_db"
        echo "Name: $first_name $last_name" >> "$text_db"
        echo "================================" >> "$text_db"

        chown "$CURRENT_USER" "$text_db"
        chmod 600 "$text_db"

        log_success "Account saved to text database"
    fi

    return 0
}

get_next_account() {
    step "Retrieving next available account..."

    if [ ! -f "$ACCOUNT_DB" ]; then
        log_warn "No account database found"
        return 1
    fi

    if command -v jq >/dev/null 2>&1; then
        # Get the first unused account
        local account=$(jq -r '.accounts[] | select(.last_used == null) | "\(.email):\(.password):\(.first_name):\(.last_name)"' "$ACCOUNT_DB" | head -1)

        if [ -n "$account" ] && [ "$account" != "null" ]; then
            echo "$account"
            return 0
        fi
    fi

    log_warn "No available accounts found"
    return 1
}

#------------
# Enhanced Reset Functions (from original script)
#------------
check_root() {
    step "Checking for root privileges..."
    if [ "$EUID" -ne 0 ]; then
        echo -e "${RED}ERROR${NC} This script requires administrator privileges."
        echo "Please run using 'sudo $0'"
        log_error "Not running as root. Exiting."
        exit 1
    fi
    log_info "Privilege check passed (running as root). Operations will affect user: $CURRENT_USER"
}

stop_cursor() {
    step "Stopping Cursor application..."
    local attempt=1
    local max_attempts=5

    while [ $attempt -le $max_attempts ]; do
        local pids
        pids=$(pgrep -if "Cursor.app" || true)

        if [ -z "$pids" ]; then
            log_info "No running Cursor processes found."
            return 0
        fi

        log_warn "Found running Cursor processes (PIDs: $pids)."
        log_warn "Attempting to gracefully shut down Cursor (Attempt $attempt/$max_attempts)..."

        echo "$pids" | xargs kill -TERM 2>/dev/null || true
        sleep 2

        pids=$(pgrep -if "Cursor.app" || true)
        if [ -z "$pids" ]; then
            log_info "Cursor processes closed successfully."
            return 0
        fi

        if [ $attempt -eq $max_attempts ]; then
            log_warn "Graceful shutdown failed, attempting force termination..."
            echo "$pids" | xargs kill -KILL 2>/dev/null || true
            sleep 1

            if pgrep -if "Cursor.app" > /dev/null; then
                log_error "Cursor processes remain even after force termination."
                exit 1
            else
                log_info "Cursor processes have been force closed."
                return 0
            fi
        fi

        ((attempt++))
        sleep 1
    done

    log_error "Failed to close all Cursor processes after $max_attempts attempts."
    exit 1
}

enhanced_clear_storage() {
    step "Enhanced storage clearing..."

    # Clear all Cursor data locations
    local dirs_to_clear=(
        "$CURSOR_SUPPORT/User/state"
        "$CURSOR_SUPPORT/User/History"
        "$CURSOR_SUPPORT/User/workspaceStorage"
        "$CURSOR_SUPPORT/logs"
        "$CURSOR_SUPPORT/CachedData"
        "$CURSOR_SUPPORT/Cache"
        "$CURSOR_SUPPORT/GPUCache"
        "$CURSOR_SUPPORT/Code Cache"
        "$CURSOR_SUPPORT/Partitions/cursor/Cookies"
        "$CURSOR_SUPPORT/Partitions/cursor/Local Storage/leveldb"
        "$CURSOR_SUPPORT/Partitions/cursor/Session Storage"
        "$CURSOR_SUPPORT/Partitions/cursor/IndexedDB"
        "$CURSOR_SUPPORT/Partitions/cursor/Service Worker"
    )

    for dir in "${dirs_to_clear[@]}"; do
        if [ -d "$dir" ]; then
            log_info "Clearing directory: $dir"
            sudo rm -rf "$dir"
            mkdir -p "$dir"
            sudo chown "$CURRENT_USER" "$dir"
            sudo chmod 755 "$dir"
        fi
    done

    # Clear additional locations
    local additional_locations=(
        "$CURRENT_USER_HOME/Library/Logs/Cursor"
        "$CURRENT_USER_HOME/Library/Preferences/com.cursor.Cursor.plist"
        "$CURRENT_USER_HOME/Library/Saved Application State/com.cursor.Cursor.savedState"
        "$CURRENT_USER_HOME/Library/WebKit/com.cursor.Cursor"
    )

    for loc in "${additional_locations[@]}"; do
        if [ -e "$loc" ]; then
            log_info "Clearing: $loc"
            sudo rm -rf "$loc"
        fi
    done

    # Clear temporary files
    find /tmp -name "*cursor*" -o -name "*Cursor*" -type f -exec sudo rm -f {} \; 2>/dev/null || true

    log_success "Enhanced storage clearing completed"
    return 0
}

enhanced_modify_storage() {
    step "Enhanced storage modification..."

    if [ ! -f "$STORAGE_JSON" ]; then
        log_info "No storage.json to modify."
        return 0
    fi

    # Backup existing storage
    local backup_file="$BACKUP_DIR/storage.json.$(date +%Y%m%d_%H%M%S).bak"
    mkdir -p "$BACKUP_DIR"
    sudo cp "$STORAGE_JSON" "$backup_file"
    sudo chown "$CURRENT_USER" "$backup_file"

    # Generate multiple new identifiers for enhanced obfuscation
    local new_uuid=$(generate_enhanced_uuid)
    local new_machine_id=$(openssl rand -hex 32)
    local new_mac_machine_id=$(openssl rand -hex 32)
    local new_device_id=$(generate_enhanced_uuid)
    local new_session_id=$(openssl rand -hex 16)

    log_info "Modifying storage.json with enhanced identifiers..."

    local temp_file=$(mktemp)

    if command -v jq &> /dev/null; then
        jq --arg uuid "$new_uuid" \
           --arg machine_id "$new_machine_id" \
           --arg mac_id "$new_mac_machine_id" \
           --arg device_id "$new_device_id" \
           --arg session_id "$new_session_id" \
           '.["telemetry.devDeviceId"] = $uuid |
            .["telemetry.machineId"] = $machine_id |
            .["telemetry.macMachineId"] = $mac_id |
            .["cursor.deviceId"] = $device_id |
            .["cursor.sessionId"] = $session_id |
            .["cursor.installId"] = $uuid |
            .["cursor.firstInstall"] = (now | todate)' \
           "$STORAGE_JSON" > "$temp_file"
    else
        # Fallback to sed
        cp "$STORAGE_JSON" "$temp_file"
        sed -i.bak "s/\"telemetry.devDeviceId\": \"[^\"]*\"/\"telemetry.devDeviceId\": \"$new_uuid\"/" "$temp_file"
        sed -i.bak "s/\"telemetry.machineId\": \"[^\"]*\"/\"telemetry.machineId\": \"$new_machine_id\"/" "$temp_file"
        sed -i.bak "s/\"telemetry.macMachineId\": \"[^\"]*\"/\"telemetry.macMachineId\": \"$new_mac_machine_id\"/" "$temp_file"
        rm -f "${temp_file}.bak"
    fi

    # Verify and replace
    if [ -s "$temp_file" ]; then
        sudo cp "$temp_file" "$STORAGE_JSON"
        sudo chown "$CURRENT_USER" "$STORAGE_JSON"
        sudo chmod 644 "$STORAGE_JSON"
        rm -f "$temp_file"
        log_success "Enhanced storage modification completed"
    else
        log_error "Failed to modify storage.json"
        rm -f "$temp_file"
        return 1
    fi

    return 0
}

#------------
# Main Workflow Functions
#------------
create_new_account() {
    step "Creating new Cursor account..."

    # Generate credentials
    local credentials=$(generate_random_credentials)
    IFS=':' read -r first_name last_name random_num <<< "$credentials"

    # Get temporary email
    local email=$(get_temporary_email)
    local password=$(generate_secure_password)

    log_info "Generated credentials:"
    log_info "  Name: $first_name $last_name"
    log_info "  Email: $email"
    log_info "  Password: [HIDDEN]"

    # Attempt registration
    if automate_cursor_registration "$email" "$password" "$first_name" "$last_name"; then
        # Save to database
        save_account_to_db "$email" "$password" "$first_name" "$last_name"
        log_success "New account created and saved successfully!"
        return 0
    else
        log_error "Failed to create new account"
        return 1
    fi
}

enhanced_reset_with_new_account() {
    step "Performing enhanced reset with new account creation..."

    # Stop Cursor
    stop_cursor

    # Randomize hardware identifiers
    randomize_hardware_identifiers
    randomize_network_identifiers

    # Clear storage
    enhanced_clear_storage

    # Modify storage with new identifiers
    enhanced_modify_storage

    # Create new account
    if create_new_account; then
        log_success "Enhanced reset with new account completed successfully!"
        log_info "You can now restart Cursor and use the new account."
        return 0
    else
        log_warn "Account creation failed, but reset was completed."
        log_info "You may need to manually create an account."
        return 1
    fi
}

use_existing_account() {
    step "Using existing account from database..."

    local account_info=$(get_next_account)
    if [ $? -eq 0 ]; then
        IFS=':' read -r email password first_name last_name <<< "$account_info"

        log_info "Using account: $email"
        log_info "Name: $first_name $last_name"

        # Mark account as used
        if command -v jq >/dev/null 2>&1; then
            local temp_db="/tmp/cursor_accounts_temp.json"
            jq --arg email "$email" '(.accounts[] | select(.email == $email) | .last_used) = (now | todate)' "$ACCOUNT_DB" > "$temp_db"
            mv "$temp_db" "$ACCOUNT_DB"
            chown "$CURRENT_USER" "$ACCOUNT_DB"
        fi

        log_success "Account information retrieved. Please use these credentials in Cursor."
        return 0
    else
        log_error "No existing accounts available. Please create a new account first."
        return 1
    fi
}

show_account_status() {
    step "Showing account database status..."

    if [ ! -f "$ACCOUNT_DB" ]; then
        log_warn "No account database found."
        return 1
    fi

    if command -v jq >/dev/null 2>&1; then
        local total_accounts=$(jq '.created_count // 0' "$ACCOUNT_DB")
        local unused_accounts=$(jq '[.accounts[] | select(.last_used == null)] | length' "$ACCOUNT_DB")
        local used_accounts=$(jq '[.accounts[] | select(.last_used != null)] | length' "$ACCOUNT_DB")

        echo -e "\n${CYAN}=== Account Database Status ===${NC}"
        echo -e "${GREEN}Total accounts created: ${WHITE}$total_accounts${NC}"
        echo -e "${GREEN}Unused accounts: ${WHITE}$unused_accounts${NC}"
        echo -e "${YELLOW}Used accounts: ${WHITE}$used_accounts${NC}"
        echo -e "${CYAN}================================${NC}\n"

        if [ "$unused_accounts" -gt 0 ]; then
            echo -e "${GREEN}You have $unused_accounts unused accounts available.${NC}"
        else
            echo -e "${YELLOW}No unused accounts available. Consider creating new accounts.${NC}"
        fi
    else
        log_warn "jq not available. Cannot show detailed account status."
        if [ -f "$HOME/.cursor_accounts.txt" ]; then
            local account_count=$(grep -c "Email:" "$HOME/.cursor_accounts.txt" 2>/dev/null || echo "0")
            echo -e "${GREEN}Text database contains approximately $account_count accounts.${NC}"
        fi
    fi

    return 0
}

#------------
# Menu System
#------------
show_enhanced_menu() {
    echo
    echo -e "${CYAN}========================================${NC}"
    echo -e "${WHITE}   Enhanced Cursor Trial Reset Tool    ${NC}"
    echo -e "${PURPLE}           Version $SCRIPT_VERSION           ${NC}"
    echo -e "${CYAN}========================================${NC}"
    echo
    echo -e "${GREEN}1)${NC} Enhanced reset with new account creation"
    echo -e "${GREEN}2)${NC} Create new account only"
    echo -e "${GREEN}3)${NC} Use existing account from database"
    echo -e "${GREEN}4)${NC} Show account database status"
    echo -e "${GREEN}5)${NC} Basic reset (original functionality)"
    echo -e "${GREEN}6)${NC} Initialize account database"
    echo -e "${GREEN}7)${NC} Exit"
    echo
    echo -n "Enter your choice [1-7]: "
    read -r choice

    case $choice in
        1)
            check_root
            initialize_account_db
            enhanced_reset_with_new_account
            ;;
        2)
            initialize_account_db
            create_new_account
            ;;
        3)
            use_existing_account
            ;;
        4)
            show_account_status
            ;;
        5)
            check_root
            stop_cursor
            enhanced_clear_storage
            enhanced_modify_storage
            log_success "Basic reset completed!"
            ;;
        6)
            initialize_account_db
            ;;
        7)
            log_info "Exiting Enhanced Cursor Reset Tool."
            exit 0
            ;;
        *)
            log_error "Invalid choice. Please try again."
            show_enhanced_menu
            ;;
    esac
}

#------------
# Main execution
#------------
main() {
    initialize_log

    # Check environment
    if ! command -v codesign >/dev/null; then
        log_error "codesign command not found. Please install Xcode Command Line Tools."
        echo "Run: xcode-select --install"
        exit 1
    fi

    # Check for required tools
    if ! command -v osascript >/dev/null; then
        log_error "osascript not found. This script requires macOS."
        exit 1
    fi

    # Show enhanced menu
    show_enhanced_menu
}

main
