#!/usr/bin/env bash

# Browser Automation Testing Script for Enhanced Cursor Reset Tool
# This script tests the browser automation functionality independently

set -euo pipefail

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Test counter
TEST_COUNT=0
PASS_COUNT=0
FAIL_COUNT=0

# Test functions
test_start() {
    ((TEST_COUNT++))
    echo -e "\n${CYAN}[TEST $TEST_COUNT]${NC} $1"
}

test_pass() {
    ((PASS_COUNT++))
    echo -e "${GREEN}✓ PASS${NC} $1"
}

test_fail() {
    ((FAIL_COUNT++))
    echo -e "${RED}✗ FAIL${NC} $1"
}

test_info() {
    echo -e "${BLUE}ℹ INFO${NC} $1"
}

# Source required functions from the main script
source_browser_functions() {
    if [ -f "5Cursor_resetgpt.sh" ]; then
        # Define logging functions first
        RED='\033[0;31m'
        GREEN='\033[0;32m'
        YELLOW='\033[0;33m'
        BLUE='\033[0;34m'
        CYAN='\033[0;36m'
        NC='\033[0m'

        _log() {
            local color_code="$1"
            local level_text="$2"
            local message="$3"
            echo -e "${color_code}[${level_text}]${NC} ${message}"
        }

        log_info() { _log "$GREEN" "INFO" "$1"; }
        log_warn() { _log "$YELLOW" "WARN" "$1"; }
        log_error() { _log "$RED" "ERROR" "$1"; }
        log_debug() { _log "$BLUE" "DEBUG" "$1"; }
        log_success() { _log "$CYAN" "SUCCESS" "$1"; }

        # Define step function
        STEP=0
        step() { ((STEP++)); echo -e "[Step $STEP] $1"; log_info "$1"; }

        # Source constants
        USER_AGENTS=(
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
        )

        # Extract and source the browser automation functions
        eval "$(sed -n '/^setup_browser_automation()/,/^}/p' 5Cursor_resetgpt.sh)"
        eval "$(sed -n '/^launch_browser_session()/,/^}/p' 5Cursor_resetgpt.sh)"
        eval "$(sed -n '/^chrome_execute_js()/,/^}/p' 5Cursor_resetgpt.sh)"
        eval "$(sed -n '/^chrome_navigate()/,/^}/p' 5Cursor_resetgpt.sh)"
        eval "$(sed -n '/^chrome_wait_for_element()/,/^}/p' 5Cursor_resetgpt.sh)"
        eval "$(sed -n '/^chrome_fill_form()/,/^}/p' 5Cursor_resetgpt.sh)"

        test_pass "Browser automation functions loaded"
    else
        test_fail "Enhanced script not found"
        exit 1
    fi
}

# Test Chrome installation
test_chrome_installation() {
    test_start "Testing Chrome installation"
    
    local chrome_path="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    if [ -f "$chrome_path" ]; then
        test_pass "Google Chrome found at: $chrome_path"
        
        # Test Chrome version
        local chrome_version=$("$chrome_path" --version 2>/dev/null | head -1)
        test_info "Chrome version: $chrome_version"
    else
        test_fail "Google Chrome not found"
        echo "Please install Google Chrome from https://www.google.com/chrome/"
        return 1
    fi
}

# Test browser setup
test_browser_setup() {
    test_start "Testing browser automation setup"
    
    local profile_dir=$(setup_browser_automation)
    if [ $? -eq 0 ] && [ -d "$profile_dir" ]; then
        test_pass "Browser profile created: $profile_dir"
        
        # Check if preferences file was created
        if [ -f "$profile_dir/Preferences" ]; then
            test_pass "Chrome preferences file created"
        else
            test_fail "Chrome preferences file not created"
        fi
        
        # Cleanup
        rm -rf "$profile_dir"
        test_info "Cleaned up test profile"
    else
        test_fail "Browser setup failed"
        return 1
    fi
}

# Test browser launch
test_browser_launch() {
    test_start "Testing browser launch with remote debugging"
    
    # Setup browser first
    local profile_dir=$(setup_browser_automation)
    if [ $? -ne 0 ]; then
        test_fail "Browser setup failed"
        return 1
    fi
    
    # Launch browser
    local chrome_pid=$(launch_browser_session "$profile_dir")
    if [ $? -eq 0 ] && [ -n "$chrome_pid" ]; then
        test_pass "Chrome launched successfully (PID: $chrome_pid)"
        
        # Test remote debugging connection
        sleep 2
        if curl -s "http://localhost:9222/json" > /dev/null 2>&1; then
            test_pass "Remote debugging port accessible"
        else
            test_fail "Remote debugging port not accessible"
        fi
        
        # Cleanup
        kill $chrome_pid 2>/dev/null || true
        sleep 1
        rm -rf "$profile_dir"
        test_info "Cleaned up browser session"
    else
        test_fail "Browser launch failed"
        rm -rf "$profile_dir"
        return 1
    fi
}

# Test JavaScript execution
test_javascript_execution() {
    test_start "Testing JavaScript execution via DevTools"
    
    # Setup and launch browser
    local profile_dir=$(setup_browser_automation)
    local chrome_pid=$(launch_browser_session "$profile_dir")
    
    if [ $? -ne 0 ]; then
        test_fail "Failed to launch browser for JS testing"
        return 1
    fi
    
    sleep 3
    
    # Test basic JavaScript execution
    local js_result=$(chrome_execute_js "2 + 2" 2>/dev/null)
    if echo "$js_result" | grep -q "4"; then
        test_pass "Basic JavaScript execution works"
    else
        test_fail "Basic JavaScript execution failed"
    fi
    
    # Test DOM access
    local dom_result=$(chrome_execute_js "document.title" 2>/dev/null)
    if [ $? -eq 0 ]; then
        test_pass "DOM access works"
        test_info "Page title: $(echo "$dom_result" | grep -o '"value":"[^"]*"' | cut -d'"' -f4)"
    else
        test_fail "DOM access failed"
    fi
    
    # Cleanup
    kill $chrome_pid 2>/dev/null || true
    rm -rf "$profile_dir"
}

# Test navigation
test_navigation() {
    test_start "Testing page navigation"
    
    # Setup and launch browser
    local profile_dir=$(setup_browser_automation)
    local chrome_pid=$(launch_browser_session "$profile_dir")
    
    if [ $? -ne 0 ]; then
        test_fail "Failed to launch browser for navigation testing"
        return 1
    fi
    
    sleep 3
    
    # Test navigation to a simple page
    if chrome_navigate "https://httpbin.org/html" 15; then
        test_pass "Navigation to test page successful"
        
        # Verify we're on the right page
        local current_url=$(chrome_execute_js "window.location.href" 2>/dev/null)
        if echo "$current_url" | grep -q "httpbin.org"; then
            test_pass "URL verification successful"
        else
            test_fail "URL verification failed"
        fi
    else
        test_fail "Navigation failed"
    fi
    
    # Cleanup
    kill $chrome_pid 2>/dev/null || true
    rm -rf "$profile_dir"
}

# Test form interaction
test_form_interaction() {
    test_start "Testing form interaction capabilities"
    
    # Setup and launch browser
    local profile_dir=$(setup_browser_automation)
    local chrome_pid=$(launch_browser_session "$profile_dir")
    
    if [ $? -ne 0 ]; then
        test_fail "Failed to launch browser for form testing"
        return 1
    fi
    
    sleep 3
    
    # Navigate to a page with forms
    if chrome_navigate "https://httpbin.org/forms/post" 15; then
        test_pass "Navigated to form test page"
        
        # Test element detection
        if chrome_wait_for_element 'input[name="custname"]' 10; then
            test_pass "Form element detection works"
            
            # Test form filling
            if chrome_fill_form 'input[name="custname"]' "Test User" "text"; then
                test_pass "Form filling works"
                
                # Verify the value was set
                local field_value=$(chrome_execute_js "document.querySelector('input[name=\"custname\"]').value" 2>/dev/null)
                if echo "$field_value" | grep -q "Test User"; then
                    test_pass "Form value verification successful"
                else
                    test_fail "Form value verification failed"
                fi
            else
                test_fail "Form filling failed"
            fi
        else
            test_fail "Form element detection failed"
        fi
    else
        test_fail "Navigation to form page failed"
    fi
    
    # Cleanup
    kill $chrome_pid 2>/dev/null || true
    rm -rf "$profile_dir"
}

# Test error handling
test_error_handling() {
    test_start "Testing error handling"
    
    # Test with invalid JavaScript
    local error_result=$(chrome_execute_js "invalid.javascript.code" 2>/dev/null)
    if [ $? -ne 0 ]; then
        test_pass "Invalid JavaScript properly handled"
    else
        test_fail "Invalid JavaScript not properly handled"
    fi
    
    # Test with non-existent element
    local profile_dir=$(setup_browser_automation)
    local chrome_pid=$(launch_browser_session "$profile_dir")
    
    if [ $? -eq 0 ]; then
        sleep 3
        
        if ! chrome_wait_for_element 'input[name="nonexistent"]' 3; then
            test_pass "Non-existent element properly handled"
        else
            test_fail "Non-existent element not properly handled"
        fi
        
        # Cleanup
        kill $chrome_pid 2>/dev/null || true
        rm -rf "$profile_dir"
    fi
}

# Test Cursor registration page access
test_cursor_page_access() {
    test_start "Testing Cursor registration page access"
    
    # Setup and launch browser
    local profile_dir=$(setup_browser_automation)
    local chrome_pid=$(launch_browser_session "$profile_dir")
    
    if [ $? -ne 0 ]; then
        test_fail "Failed to launch browser for Cursor page testing"
        return 1
    fi
    
    sleep 3
    
    # Try to navigate to Cursor registration page
    if chrome_navigate "https://authenticator.cursor.sh/sign-up" 20; then
        test_pass "Successfully navigated to Cursor registration page"
        
        # Check page content
        local page_title=$(chrome_execute_js "document.title" 2>/dev/null)
        test_info "Page title: $(echo "$page_title" | grep -o '"value":"[^"]*"' | cut -d'"' -f4)"
        
        # Look for common form elements
        local form_elements=$(chrome_execute_js "
            var inputs = document.querySelectorAll('input');
            var result = [];
            for (var i = 0; i < inputs.length; i++) {
                result.push(inputs[i].name + ':' + inputs[i].type);
            }
            result.join(', ');
        " 2>/dev/null)
        
        if [ -n "$form_elements" ]; then
            test_info "Found form elements: $(echo "$form_elements" | grep -o '"value":"[^"]*"' | cut -d'"' -f4)"
            test_pass "Form elements detected on Cursor page"
        else
            test_warn "No form elements detected (page may have changed)"
        fi
    else
        test_fail "Failed to navigate to Cursor registration page"
        test_info "This may be due to network issues or page changes"
    fi
    
    # Cleanup
    kill $chrome_pid 2>/dev/null || true
    rm -rf "$profile_dir"
}

# Main test execution
main() {
    echo -e "${CYAN}========================================${NC}"
    echo -e "${CYAN}  Browser Automation Testing Suite     ${NC}"
    echo -e "${CYAN}========================================${NC}"
    
    # Source the browser functions
    source_browser_functions
    
    # Run all tests
    test_chrome_installation
    test_browser_setup
    test_browser_launch
    test_javascript_execution
    test_navigation
    test_form_interaction
    test_error_handling
    test_cursor_page_access
    
    # Show results
    echo -e "\n${CYAN}========================================${NC}"
    echo -e "${CYAN}              Test Results              ${NC}"
    echo -e "${CYAN}========================================${NC}"
    echo -e "${GREEN}Passed: $PASS_COUNT${NC}"
    echo -e "${RED}Failed: $FAIL_COUNT${NC}"
    echo -e "${BLUE}Total:  $TEST_COUNT${NC}"
    
    if [ $FAIL_COUNT -eq 0 ]; then
        echo -e "\n${GREEN}🎉 All browser automation tests passed!${NC}"
        echo -e "${GREEN}The enhanced browser automation should work correctly.${NC}"
        exit 0
    else
        echo -e "\n${YELLOW}⚠️  Some tests failed. Please review the issues above.${NC}"
        echo -e "${YELLOW}The browser automation may not work as expected.${NC}"
        exit 1
    fi
}

main "$@"
