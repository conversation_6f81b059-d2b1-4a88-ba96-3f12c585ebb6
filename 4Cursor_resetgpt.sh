#!/usr/bin/env bash

# macOS Cursor Application Reset and Modification Script (Improved Version)
#
# Important Notes:
# - This script modifies Cursor application internal files to reset device identification logic.
# - Modifying the application may violate Cursor's Terms of Service. Use at your own risk.
# - The script's success rate depends on the specific Cursor version; updates may break the script.
# - It is recommended to back up important data before execution.
# - Consider purchasing a legitimate license to support the developers.

# Exit on errors, undefined vars, and pipe failures
set -euo pipefail

# Check macOS
if [[ "$(uname)" != "Darwin" ]]; then
    echo "Error: This script requires macOS. Exiting." >&2
    exit 1
fi

# Steps counter
STEP=0
step() { ((STEP++)); echo -e "[Step $STEP] $1"; log_info "$1"; }

echo "Starting Cursor App reset & modification..."

#------------
# Logging Setup
#------------
LOG_DIR="$HOME/Library/Logs"
mkdir -p "$LOG_DIR"
LOG_FILE="$LOG_DIR/cursor_reset.log"

initialize_log() {
    echo "========== Cursor Reset Log Start: $(date) ==========" > "$LOG_FILE"
    chmod 644 "$LOG_FILE"
    if [ "$EUID" -eq 0 ] && [ -n "${SUDO_USER:-}" ]; then
        chown "$SUDO_USER" "$LOG_FILE"
    fi
    echo "Log file located at: $LOG_FILE"
}

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Base logging function
_log() {
    local color_code="$1"
    local level_text="$2"
    local message="$3"
    echo -e "${color_code}[${level_text}]${NC} ${message}"
    # Ensure log entry format is consistent, remove echo -e color codes
    local log_message=$(echo -e "${message}" | sed 's/\x1b\[[0-9;]*m//g')
    echo "[${level_text}] $(date '+%Y-%m-%d %H:%M:%S') ${log_message}" >> "$LOG_FILE"
}

log_info() { _log "$GREEN" "INFO" "$1"; }
log_warn() { _log "$YELLOW" "WARN" "$1"; }
log_error() { _log "$RED" "ERROR" "$1"; }
log_debug() { _log "$BLUE" "DEBUG" "$1"; }

#------------
# Helpers
#------------
get_current_user() {
    if [ "$EUID" -eq 0 ] && [ -n "${SUDO_USER:-}" ]; then
        echo "$SUDO_USER"
    else
        echo "$USER"
    fi
}

CURRENT_USER=$(get_current_user)
if [ -z "$CURRENT_USER" ]; then
    echo -e "${RED}ERROR${NC} Cannot get a valid current username." >&2
    exit 1
fi

CURRENT_USER_HOME=$(eval echo "~$CURRENT_USER")

# Define paths
CURSOR_SUPPORT="$CURRENT_USER_HOME/Library/Application Support/Cursor"
STORAGE_JSON="$CURSOR_SUPPORT/User/globalStorage/storage.json"
BACKUP_DIR="$CURSOR_SUPPORT/User/globalStorage/backups"
CURSOR_APP="/Applications/Cursor.app"
APP_BACKUP_DIR="$CURSOR_SUPPORT/backups/app_backups"
CURSOR_CACHE="$CURRENT_USER_HOME/Library/Caches/cursor-updater"
CURSOR_FREE_VIP="$CURRENT_USER_HOME/.cursor-free-vip"
CURSOR_LOGS="$CURRENT_USER_HOME/Library/Logs/Cursor"
CURSOR_PREFERENCES="$CURRENT_USER_HOME/Library/Preferences/com.cursor.Cursor.plist"
CURSOR_STATE="$CURSOR_SUPPORT/User/state"
CURSOR_COOKIES="$CURSOR_SUPPORT/Partitions/cursor/Cookies"
CURSOR_LOCAL_STORAGE="$CURSOR_SUPPORT/Partitions/cursor/Local Storage/leveldb"
CURSOR_SESSION_STORAGE="$CURSOR_SUPPORT/Partitions/cursor/Session Storage"
CURSOR_INDEXED_DB="$CURSOR_SUPPORT/Partitions/cursor/IndexedDB"

#------------
# Ensure running as root
#------------
check_root() {
    step "Checking for root privileges..."
    if [ "$EUID" -ne 0 ]; then
        echo -e "${RED}ERROR${NC} This script requires administrator privileges."
        echo "Please run using 'sudo $0'"
        log_error "Not running as root. Exiting."
        exit 1
    fi
    log_info "Privilege check passed (running as root). Operations will affect user: $CURRENT_USER"
}

#------------
# Stop Cursor process
#------------
stop_cursor() {
    step "Stopping Cursor application..."
    local attempt=1
    local max_attempts=5

    while [ $attempt -le $max_attempts ]; do
        # Get PIDs
        local pids
        pids=$(pgrep -if "Cursor.app" || true)

        if [ -z "$pids" ]; then
            log_info "No running Cursor processes found."
            return 0
        fi

        log_warn "Found running Cursor processes (PIDs: $pids)."
        log_debug "Process details:"
        ps -p $pids 2>/dev/null || log_warn "Could not get details for some processes"

        log_warn "Attempting to gracefully shut down Cursor (Attempt $attempt/$max_attempts)..."

        # Try sending TERM signal first
        echo "$pids" | xargs kill -TERM 2>/dev/null || true
        sleep 2

        # Check if processes are still running
        pids=$(pgrep -if "Cursor.app" || true)
        if [ -z "$pids" ]; then
            log_info "Cursor processes closed successfully."
            return 0
        fi

        # If last attempt, try KILL signal
        if [ $attempt -eq $max_attempts ]; then
            log_warn "Graceful shutdown failed, attempting force termination..."
            echo "$pids" | xargs kill -KILL 2>/dev/null || true
            sleep 1

            # Final check
            if pgrep -if "Cursor.app" > /dev/null; then
                log_error "Cursor processes remain even after force termination. Please close them manually and retry."
                exit 1
            else
                log_info "Cursor processes have been force closed."
                return 0
            fi
        fi

        ((attempt++))
        sleep 1
    done

    log_error "Failed to close all Cursor processes after $max_attempts attempts."
    log_error "Please manually close all Cursor processes and re-run the script."
    exit 1
}

#------------
# Backup storage.json
#------------
backup_storage() {
    step "Backing up storage.json..."
    if [ ! -f "$STORAGE_JSON" ]; then
        log_info "No storage.json to back up."
        return 0
    fi

    mkdir -p "$BACKUP_DIR"
    sudo chown "$CURRENT_USER" "$BACKUP_DIR"
    sudo chmod 755 "$BACKUP_DIR"

    local backup_file="$BACKUP_DIR/storage.json.$(date +%Y%m%d_%H%M%S).bak"

    if sudo cp "$STORAGE_JSON" "$backup_file"; then
        sudo chmod 644 "$backup_file"
        sudo chown "$CURRENT_USER" "$backup_file"
        log_info "Configuration file backed up to: $backup_file"
    else
        log_error "Failed to back up configuration file!"
        return 1
    fi
    return 0
}

#------------
# Generate random UUID
#------------
generate_uuid() {
    uuidgen | tr '[:upper:]' '[:lower:]'
}

#------------
# Process storage.json
#------------
modify_storage() {
    step "Processing storage.json..."
    if [ ! -f "$STORAGE_JSON" ]; then
        log_info "No storage.json to modify."
        return 0
    fi

    backup_storage

    # Generate new UUIDs and IDs
    local new_uuid=$(generate_uuid)
    local new_machine_id=$(openssl rand -hex 32)
    local new_mac_machine_id=$(openssl rand -hex 32)

    log_info "Modifying storage.json with new identifiers..."

    # Create a temporary file
    local temp_file=$(mktemp)

    # Use jq to modify the JSON file if available
    if command -v jq &> /dev/null; then
        log_info "Using jq to modify storage.json..."
        jq --arg uuid "$new_uuid" \
           --arg machine_id "$new_machine_id" \
           --arg mac_id "$new_mac_machine_id" \
           '.["telemetry.devDeviceId"] = $uuid |
            .["telemetry.machineId"] = $machine_id |
            .["telemetry.macMachineId"] = $mac_id' \
           "$STORAGE_JSON" > "$temp_file"
    else
        # Fallback to sed if jq is not available
        log_warn "jq not found, using sed for JSON modification (less reliable)..."
        cp "$STORAGE_JSON" "$temp_file"

        # Replace UUIDs with sed (this is less reliable than jq)
        sed -i.bak "s/\"telemetry.devDeviceId\": \"[^\"]*\"/\"telemetry.devDeviceId\": \"$new_uuid\"/" "$temp_file"
        sed -i.bak "s/\"telemetry.machineId\": \"[^\"]*\"/\"telemetry.machineId\": \"$new_machine_id\"/" "$temp_file"
        sed -i.bak "s/\"telemetry.macMachineId\": \"[^\"]*\"/\"telemetry.macMachineId\": \"$new_mac_machine_id\"/" "$temp_file"
        rm -f "${temp_file}.bak"
    fi

    # Verify the temp file is valid JSON
    if [ -s "$temp_file" ]; then
        # Check if the file is valid JSON
        if command -v jq &> /dev/null; then
            if ! jq . "$temp_file" > /dev/null 2>&1; then
                log_error "Modified storage.json is not valid JSON. Restoring backup."
                rm -f "$temp_file"
                return 1
            fi
        fi

        # Replace the original file
        sudo cp "$temp_file" "$STORAGE_JSON"
        sudo chown "$CURRENT_USER" "$STORAGE_JSON"
        sudo chmod 644 "$STORAGE_JSON"
        rm -f "$temp_file"

        log_info "Successfully modified storage.json with new identifiers."
    else
        log_error "Failed to modify storage.json - temp file is empty."
        rm -f "$temp_file"
        return 1
    fi

    log_info "Storage file processing complete."
    return 0
}

#------------
# Modify application JS files
#------------
modify_app() {
    step "Patching Cursor.app files..."
    if [ ! -d "$CURSOR_APP" ]; then
        log_error "Cursor.app not found at $CURSOR_APP"
        exit 1
    fi

    # Define target files
    local targets=(
        "Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js"
        "Contents/Resources/app/out/main.js"
        "Contents/Resources/app/out/vs/code/node/cliProcessMain.js"
        "Contents/Resources/app/out/vs/platform/product/common/product.js"
        "Contents/Resources/app/out/vs/platform/telemetry/common/telemetryUtils.js"
        "Contents/Resources/app/out/vs/platform/telemetry/node/telemetryNodeUtils.js"
        "Contents/Resources/app/out/vs/platform/diagnostics/node/diagnosticsService.js"
        "Contents/Resources/app/out/vs/platform/request/common/request.js"
        "Contents/Resources/app/out/vs/platform/request/node/request.js"
    )

    # Create backup directory
    local timestamp=$(date +%Y%m%d_%H%M%S)
    mkdir -p "$APP_BACKUP_DIR"
    sudo chown "$CURRENT_USER" "$APP_BACKUP_DIR"
    local backup_app="$APP_BACKUP_DIR/Cursor.app.backup_$timestamp"

    log_info "Creating backup of Cursor.app to: $backup_app"
    sudo cp -a "$CURSOR_APP" "$backup_app" || {
        log_error "Failed to back up application!"
        exit 1
    }

    # Create temporary working directory
    local tmpdir="/tmp/cursor_mod_$timestamp"
    mkdir -p "$tmpdir"
    log_info "Creating temporary working copy in: $tmpdir"

    sudo cp -a "$CURSOR_APP" "$tmpdir/Cursor.app"
    sudo chmod -R u+w "$tmpdir/Cursor.app"

    # Remove signature
    log_info "Removing application signature..."
    sudo codesign --remove-signature "$tmpdir/Cursor.app" 2>/dev/null || log_warn "Failed to remove main signature"

    # Remove signatures of helper apps
    local components=(
        "$tmpdir/Cursor.app/Contents/Frameworks/Cursor Helper.app"
        "$tmpdir/Cursor.app/Contents/Frameworks/Cursor Helper (GPU).app"
        "$tmpdir/Cursor.app/Contents/Frameworks/Cursor Helper (Plugin).app"
        "$tmpdir/Cursor.app/Contents/Frameworks/Cursor Helper (Renderer).app"
    )

    for component in "${components[@]}"; do
        if [ -e "$component" ]; then
            sudo codesign --remove-signature "$component" 2>/dev/null || log_warn "Failed to remove signature for ${component##*/}"
        fi
    done

    # Patch the files
    local patched=false
    for frel in "${targets[@]}"; do
        local f="$tmpdir/Cursor.app/$frel"
        if [ -f "$f" ]; then
            log_info "Processing file: $frel"

            # Create backup of the file
            sudo cp "$f" "$f.orig"

            # Apply different patch strategies
            if [[ "$frel" == *"extensionHostProcess.js"* ]]; then
                # Try checksum patch
                if grep -q 'i.header.set("x-cursor-checksum' "$f"; then
                    log_debug "Found x-cursor-checksum code, applying patch..."
                    sudo sed -i.tmp 's/i\.header\.set("x-cursor-checksum",e===void 0?`${p}${t}`:`${p}${t}\/${e}`)/i.header.set("x-cursor-checksum",e===void 0?`${p}${t}`:`${p}${t}\/${p}`)/' "$f" && {
                        log_info "Successfully patched checksum code in $frel"
                        patched=true
                    }
                fi

                # Patch any trial-related code
                if grep -q 'trialEnded' "$f"; then
                    log_debug "Found trialEnded code, applying patch..."
                    sudo sed -i.tmp 's/trialEnded/trialNotEnded/g' "$f" && {
                        log_info "Successfully patched trial status in $frel"
                        patched=true
                    }
                fi

                # Patch any license check code
                if grep -q 'isLicensed' "$f"; then
                    log_debug "Found license check code, applying patch..."
                    sudo sed -i.tmp 's/isLicensed\s*=\s*\([^;]*\)/isLicensed=true/g' "$f" && {
                        log_info "Successfully patched license check in $frel"
                        patched=true
                    }
                fi
            fi

            # Try IOPlatformUUID patch
            if grep -q "IOPlatformUUID" "$f"; then
                log_debug "Found IOPlatformUUID, applying randomUUID patch..."

                # Try different patterns
                if grep -q 'function a\$(t){switch' "$f"; then
                    sudo sed -i.tmp 's/function a\$(t){switch/function a\$(t){return crypto.randomUUID(); \/* Cursor Reset Patch *\/ switch/' "$f" && {
                        log_info "Successfully patched a\$ function in $frel"
                        patched=true
                    }
                elif grep -q 'async function v5(t){let e=' "$f"; then
                    sudo sed -i.tmp 's/async function v5(t){let e=/async function v5(t){return crypto.randomUUID(); \/* Cursor Reset Patch *\/ let e=/' "$f" && {
                        log_info "Successfully patched v5 function in $frel"
                        patched=true
                    }
                else
                    # Generic replacement
                    sudo sed -i.tmp 's/IOPlatformUUID/crypto.randomUUID()/g' "$f" && {
                        log_info "Applied generic IOPlatformUUID replacement in $frel"
                        patched=true
                    }
                fi
            fi

            # Patch product.js to modify trial behavior
            if [[ "$frel" == *"product.js"* ]]; then
                log_debug "Processing product.js file..."

                # Patch trial period checks
                if grep -q 'trialPeriod' "$f"; then
                    log_debug "Found trialPeriod, applying patch..."
                    sudo sed -i.tmp 's/trialPeriod:[^,}]*/trialPeriod:9999/g' "$f" && {
                        log_info "Successfully extended trial period in $frel"
                        patched=true
                    }
                fi

                # Patch product info
                if grep -q 'licenseType' "$f"; then
                    log_debug "Found licenseType, applying patch..."
                    sudo sed -i.tmp 's/licenseType:[^,}]*/licenseType:"pro"/g' "$f" && {
                        log_info "Successfully patched license type in $frel"
                        patched=true
                    }
                fi
            fi

            # Patch telemetry
            if [[ "$frel" == *"telemetry"* ]]; then
                log_debug "Processing telemetry file..."

                # Disable telemetry
                if grep -q 'sendTelemetry' "$f"; then
                    log_debug "Found sendTelemetry, applying patch..."
                    sudo sed -i.tmp 's/sendTelemetry\s*=\s*\([^;]*\)/sendTelemetry=false/g' "$f" && {
                        log_info "Successfully disabled telemetry in $frel"
                        patched=true
                    }
                fi

                # Randomize machine ID
                if grep -q 'getMachineId' "$f"; then
                    log_debug "Found getMachineId, applying patch..."
                    sudo sed -i.tmp 's/getMachineId\s*=\s*[^{]*{/getMachineId = function() { return crypto.randomUUID(); \/\* Cursor Reset Patch \*\/ /g' "$f" && {
                        log_info "Successfully patched getMachineId in $frel"
                        patched=true
                    }
                fi
            fi

            # Patch request handling
            if [[ "$frel" == *"request"* ]]; then
                log_debug "Processing request file..."

                # Patch headers
                if grep -q 'x-cursor-machine-id' "$f"; then
                    log_debug "Found x-cursor-machine-id, applying patch..."
                    sudo sed -i.tmp 's/x-cursor-machine-id[^"]*/x-cursor-machine-id": crypto.randomUUID()/g' "$f" && {
                        log_info "Successfully patched machine ID header in $frel"
                        patched=true
                    }
                fi
            fi

            # Clean up temporary files
            sudo rm -f "$f.tmp"

            # If no patches were applied, restore original
            if [ "$patched" = false ]; then
                log_warn "No patches applied to $frel"
                sudo mv "$f.orig" "$f"
            else
                sudo rm -f "$f.orig"
            fi
        fi
    done

    if [ "$patched" = false ]; then
        log_error "No patches were applied to any files. Cursor version might be incompatible."
        log_error "Aborting modification process. Application remains unchanged."
        sudo rm -rf "$tmpdir"
        exit 1
    fi

    # Re-sign the application
    step "Re-signing the modified application..."
    local max_retry=3
    local retry_count=0
    local sign_success=false

    while [ $retry_count -lt $max_retry ] && [ "$sign_success" = false ]; do
        ((retry_count++))
        log_info "Signing attempt $retry_count/$max_retry..."

        if sudo codesign --sign - --force --deep --preserve-metadata=identifier,entitlements,flags "$tmpdir/Cursor.app" 2>/dev/null; then
            # Verify signature
            if sudo codesign --verify --verbose=2 "$tmpdir/Cursor.app" 2>/dev/null; then
                log_info "Signature verification successful!"
                sign_success=true
            else
                log_warn "Signature verification failed (Attempt $retry_count)"
                sudo codesign --remove-signature "$tmpdir/Cursor.app" 2>/dev/null || true
                sleep 1
            fi
        else
            log_warn "Signing failed (Attempt $retry_count)"
            sleep 1
        fi
    done

    if [ "$sign_success" = false ]; then
        log_error "Application signing failed after $max_retry attempts."
        log_error "The modified application might not run correctly."
    fi

    # Replace original application
    step "Installing modified application..."
    sudo rm -rf "$CURSOR_APP"
    sudo cp -a "$tmpdir/Cursor.app" "$CURSOR_APP"
    sudo chown -R root:wheel "$CURSOR_APP"
    sudo chmod -R 755 "$CURSOR_APP"

    # Remove quarantine attribute
    sudo xattr -rd com.apple.quarantine "$CURSOR_APP" 2>/dev/null || log_warn "Failed to remove quarantine attribute"

    # Clean up
    sudo rm -rf "$tmpdir"

    log_info "Cursor.app modification complete!"
    log_info "Original application backed up to: $backup_app"
}

#------------
# Disable auto-update
#------------
disable_update() {
    step "Disabling auto-update..."
    local yml="$CURSOR_APP/Contents/Resources/app-update.yml"

    if [ -f "$yml" ]; then
        sudo cp "$yml" "$yml.bak"
        sudo chown root:wheel "$yml"
        sudo chmod 444 "$yml"
        log_info "Disabled updates via $yml permissions."
    else
        log_warn "No update config found at $yml; skipping."
    fi

    local cache="$CURRENT_USER_HOME/Library/Caches/cursor-updater"
    mkdir -p "$cache"
    sudo chown root:wheel "$cache"
    sudo chmod 555 "$cache"
    log_info "Cache directory set to read-only: $cache"
}

#------------
# Clear all storage locations
#------------
clear_all_storage() {
    step "Clearing all Cursor storage locations..."

    # Clear all Cursor data in Application Support
    log_info "Clearing all Cursor data in Application Support..."

    # Create a list of directories to check and clear
    local dirs_to_clear=(
        # Main directories
        "$CURSOR_SUPPORT/User/state"
        "$CURSOR_SUPPORT/User/History"
        "$CURSOR_SUPPORT/User/workspaceStorage"
        "$CURSOR_SUPPORT/logs"
        "$CURSOR_SUPPORT/CachedData"
        "$CURSOR_SUPPORT/Cache"
        "$CURSOR_SUPPORT/GPUCache"
        "$CURSOR_SUPPORT/Code Cache"

        # Partitions directories (may not exist in all versions)
        "$CURSOR_SUPPORT/Partitions/cursor/Cookies"
        "$CURSOR_SUPPORT/Partitions/cursor/Local Storage/leveldb"
        "$CURSOR_SUPPORT/Partitions/cursor/Session Storage"
        "$CURSOR_SUPPORT/Partitions/cursor/IndexedDB"
        "$CURSOR_SUPPORT/Partitions/cursor/Service Worker"
    )

    # Clear each directory if it exists
    for dir in "${dirs_to_clear[@]}"; do
        if [ -d "$dir" ]; then
            log_info "Clearing directory: $dir"
            sudo rm -rf "$dir"
            mkdir -p "$dir"
            sudo chown "$CURRENT_USER" "$dir"
            sudo chmod 755 "$dir"
        fi
    done

    # Clear logs directory
    local logs_dir="$CURRENT_USER_HOME/Library/Logs/Cursor"
    if [ -d "$logs_dir" ]; then
        log_info "Clearing logs directory: $logs_dir"
        sudo rm -rf "$logs_dir"
        mkdir -p "$logs_dir"
        sudo chown "$CURRENT_USER" "$logs_dir"
        sudo chmod 755 "$logs_dir"
    fi

    # Reset preferences
    local prefs_file="$CURRENT_USER_HOME/Library/Preferences/com.cursor.Cursor.plist"
    if [ -f "$prefs_file" ]; then
        log_info "Resetting preferences: $prefs_file"
        sudo rm -f "$prefs_file"
    fi

    # Clear any other potential storage locations
    local other_locations=(
        "$CURRENT_USER_HOME/Library/Saved Application State/com.cursor.Cursor.savedState"
        "$CURRENT_USER_HOME/Library/WebKit/com.cursor.Cursor"
    )

    for loc in "${other_locations[@]}"; do
        if [ -e "$loc" ]; then
            log_info "Clearing: $loc"
            sudo rm -rf "$loc"
        fi
    done

    # Find and remove any Cursor-related files in the temporary directory
    log_info "Clearing Cursor-related temporary files..."
    find /tmp -name "*cursor*" -o -name "*Cursor*" -type f -exec sudo rm -f {} \; 2>/dev/null || true

    log_info "All storage locations cleared successfully."
    return 0
}

#------------
# Modify system identifiers
#------------
modify_system_identifiers() {
    step "Modifying system identifiers..."

    # Create a random hardware UUID
    local hw_uuid=$(generate_uuid)
    log_info "Generated new hardware UUID: $hw_uuid"

    # Create a file with the modified hardware UUID
    local hw_uuid_file="/tmp/cursor_hw_uuid"
    echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>
<!DOCTYPE plist PUBLIC \"-//Apple//DTD PLIST 1.0//EN\" \"http://www.apple.com/DTDs/PropertyList-1.0.dtd\">
<plist version=\"1.0\">
<dict>
    <key>IOPlatformUUID</key>
    <string>$hw_uuid</string>
</dict>
</plist>" > "$hw_uuid_file"

    # Create a script to override system calls
    local override_script="/tmp/cursor_override.js"
    echo "// Cursor system call override
const originalExec = require('child_process').exec;
const fs = require('fs');

// Override exec to intercept system UUID queries
require('child_process').exec = function(command, options, callback) {
    if (typeof options === 'function') {
        callback = options;
        options = undefined;
    }

    // Intercept ioreg commands that query platform UUID
    if (command.includes('ioreg') && command.includes('IOPlatformUUID')) {
        const fakeUuid = '$hw_uuid';
        if (callback) {
            callback(null, fakeUuid, '');
        }
        return { stdout: { on: () => {} }, stderr: { on: () => {} }, on: (event, handler) => {
            if (event === 'exit') {
                handler(0);
            }
            return { stdout: { on: () => {} }, stderr: { on: () => {} } };
        }};
    }

    // Pass through other commands
    return originalExec(command, options, callback);
};" > "$override_script"

    # Copy the override script to the Cursor app
    local app_preload_dir="$CURSOR_APP/Contents/Resources/app/preload"
    sudo mkdir -p "$app_preload_dir"
    sudo cp "$override_script" "$app_preload_dir/cursor_override.js"
    sudo chmod 644 "$app_preload_dir/cursor_override.js"

    # Modify the app's main.js to load our override script
    local main_js="$CURSOR_APP/Contents/Resources/app/out/main.js"
    if [ -f "$main_js" ]; then
        # Check if the override is already in place
        if ! grep -q "cursor_override.js" "$main_js"; then
            # Create a backup
            sudo cp "$main_js" "$main_js.bak"

            # Find the line where app is initialized
            local insert_line=$(grep -n "app.whenReady" "$main_js" | head -1 | cut -d: -f1)

            if [ -n "$insert_line" ]; then
                # Insert our preload code
                sudo sed -i.tmp "${insert_line}i\\
// Cursor Reset Patch - Load system call overrides\\
try {\\
  require(path.join(__dirname, '../preload/cursor_override.js'));\\
} catch (e) {\\
  console.error('Failed to load cursor override:', e);\\
}" "$main_js"

                log_info "Successfully injected system call override into main.js"
            else
                log_warn "Could not find insertion point in main.js"
            fi

            # Clean up
            sudo rm -f "$main_js.tmp"
        else
            log_info "System call override already present in main.js"
        fi
    else
        log_warn "Could not find main.js at $main_js"
    fi

    # Clean up temporary files
    rm -f "$hw_uuid_file" "$override_script"

    log_info "System identifiers modification complete."
    return 0
}

#------------
# Create cursor-free-vip file
#------------
create_free_vip() {
    step "Creating cursor-free-vip file..."

    # Generate a random timestamp for the file
    local timestamp=$(date -v-$((RANDOM % 30 + 1))d +%s)
    local content="{\"timestamp\": $timestamp}"

    # Create the file
    echo "$content" > "$CURSOR_FREE_VIP"
    chmod 644 "$CURSOR_FREE_VIP"
    chown "$CURRENT_USER" "$CURSOR_FREE_VIP"

    log_info "Created cursor-free-vip file with timestamp: $(date -r $timestamp)"
    return 0
}

#------------
# Fix "app is damaged" issue
#------------
fix_damaged_app() {
    step "Fixing potential 'app is damaged' issues..."

    if [ ! -d "$CURSOR_APP" ]; then
        log_error "Cursor.app not found at $CURSOR_APP"
        return 1
    fi

    sudo xattr -rd com.apple.quarantine "$CURSOR_APP" 2>/dev/null
    sudo codesign --force --deep --sign - "$CURSOR_APP" 2>/dev/null

    log_info "Applied fixes for potential 'app is damaged' issues."
    log_info "If you still see this error, try opening the app from Finder with right-click > Open."
}

#------------
# Restore from backup
#------------
restore_from_backup() {
    step "Checking for available backups..."

    if [ ! -d "$APP_BACKUP_DIR" ]; then
        log_warn "No backup directory found at $APP_BACKUP_DIR"
        return 1
    fi

    # Find the latest backup
    local latest_backup
    latest_backup=$(find "$APP_BACKUP_DIR" -name "Cursor.app.backup_*" -type d -maxdepth 1 -print 2>/dev/null | sort -r | head -n 1)

    if [ -z "$latest_backup" ] || [ ! -d "$latest_backup" ]; then
        log_warn "No Cursor.app backups found."
        return 1
    fi

    log_info "Found backup: $latest_backup"
    echo -e "${YELLOW}Do you want to restore this backup? (y/n)${NC}"
    read -r response

    if [[ "$response" =~ ^[Yy]$ ]]; then
        stop_cursor

        log_info "Removing current application..."
        sudo rm -rf "$CURSOR_APP"

        log_info "Restoring from backup..."
        sudo cp -a "$latest_backup" "$CURSOR_APP"
        sudo chown -R root:wheel "$CURSOR_APP"
        sudo chmod -R 755 "$CURSOR_APP"

        # Remove quarantine attribute
        sudo xattr -rd com.apple.quarantine "$CURSOR_APP" 2>/dev/null

        log_info "Successfully restored Cursor.app from backup."
        return 0
    else
        log_info "Backup restoration cancelled."
        return 1
    fi
}

#------------
# Show file tree structure
#------------
show_file_tree() {
    log_info "Overview of relevant file structure:"
    echo -e "${BLUE}$CURSOR_SUPPORT${NC}"
    echo "├── User/globalStorage/"
    if [ -f "$STORAGE_JSON" ]; then
        echo "│   ├── storage.json"
    else
        echo "│   ├── storage.json (Does not exist)"
    fi

    if [ -d "$BACKUP_DIR" ]; then
        echo "│   └── backups/"
        ls -1 "$BACKUP_DIR" 2>/dev/null | grep 'storage\.json\..*\.bak' | sed 's/^/│       └── /' | head -n 3
        if [ "$(ls -1 "$BACKUP_DIR" 2>/dev/null | grep 'storage\.json\..*\.bak' | wc -l)" -gt 3 ]; then
            echo "│           ..."
        fi
    fi

    if [ -d "$APP_BACKUP_DIR" ]; then
        echo "├── backups/app_backups/"
        ls -1 "$APP_BACKUP_DIR" 2>/dev/null | grep 'Cursor\.app\.backup_' | sed 's/^/│   └── /' | head -n 3
        if [ "$(ls -1 "$APP_BACKUP_DIR" 2>/dev/null | grep 'Cursor\.app\.backup_' | wc -l)" -gt 3 ]; then
            echo "│       ..."
        fi
    fi
    echo
}

#------------
# Main menu
#------------
show_menu() {
    echo
    echo -e "${GREEN}================================${NC}"
    echo -e "${BLUE}   Cursor Trial Reset Tool      ${NC}"
    echo -e "${GREEN}================================${NC}"
    echo
    echo "1) Reset Cursor trial (recommended)"
    echo "2) Restore from backup"
    echo "3) Fix 'app is damaged' issue"
    echo "4) Exit"
    echo
    echo -n "Enter your choice [1-4]: "
    read -r choice

    case $choice in
        1)
            check_root
            stop_cursor
            backup_storage
            clear_all_storage
            modify_storage
            modify_app
            modify_system_identifiers
            disable_update
            create_free_vip
            fix_damaged_app
            show_file_tree
            log_info "Cursor trial reset complete! Please restart Cursor."
            ;;
        2)
            check_root
            restore_from_backup
            ;;
        3)
            check_root
            fix_damaged_app
            ;;
        4)
            log_info "Exiting."
            exit 0
            ;;
        *)
            log_error "Invalid choice. Please try again."
            show_menu
            ;;
    esac
}

#------------
# Main execution
#------------
main() {
    initialize_log

    # Check environment
    if ! command -v codesign >/dev/null; then
        log_error "codesign command not found. Please install Xcode Command Line Tools."
        echo "Run: xcode-select --install"
        exit 1
    fi

    # Show menu
    show_menu
}

main
