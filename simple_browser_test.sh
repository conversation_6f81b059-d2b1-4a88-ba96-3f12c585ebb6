#!/usr/bin/env bash

# Simple Browser Test for Enhanced Cursor Reset Tool
set -euo pipefail

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Logging functions
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_success() { echo -e "${CYAN}[SUCCESS]${NC} $1"; }

# Test Chrome installation
test_chrome() {
    echo -e "\n${CYAN}=== Testing Chrome Installation ===${NC}"
    
    local chrome_path="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    if [ -f "$chrome_path" ]; then
        log_success "Google Chrome found"
        local version=$("$chrome_path" --version 2>/dev/null)
        log_info "Version: $version"
        return 0
    else
        log_error "Google Chrome not found"
        return 1
    fi
}

# Test Chrome launch with remote debugging
test_chrome_launch() {
    echo -e "\n${CYAN}=== Testing Chrome Launch ===${NC}"
    
    # Kill any existing Chrome processes
    pkill -f "chrome.*--remote-debugging-port=9222" 2>/dev/null || true
    sleep 2
    
    # Create temporary profile
    local profile_dir="/tmp/test_chrome_profile_$$"
    mkdir -p "$profile_dir"
    
    # Launch Chrome with remote debugging
    local chrome_args=(
        "--user-data-dir=$profile_dir"
        "--remote-debugging-port=9222"
        "--no-first-run"
        "--disable-extensions"
        "--no-sandbox"
        "--headless"
    )
    
    log_info "Launching Chrome with remote debugging..."
    "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" "${chrome_args[@]}" > /dev/null 2>&1 &
    local chrome_pid=$!
    
    # Wait for Chrome to start
    local max_wait=10
    local wait_count=0
    while [ $wait_count -lt $max_wait ]; do
        if curl -s "http://localhost:9222/json" > /dev/null 2>&1; then
            log_success "Chrome launched successfully (PID: $chrome_pid)"
            log_success "Remote debugging port accessible"
            
            # Cleanup
            kill $chrome_pid 2>/dev/null || true
            rm -rf "$profile_dir"
            return 0
        fi
        sleep 1
        ((wait_count++))
    done
    
    log_error "Failed to launch Chrome with remote debugging"
    kill $chrome_pid 2>/dev/null || true
    rm -rf "$profile_dir"
    return 1
}

# Test JavaScript execution
test_javascript() {
    echo -e "\n${CYAN}=== Testing JavaScript Execution ===${NC}"
    
    # Kill any existing Chrome processes
    pkill -f "chrome.*--remote-debugging-port=9222" 2>/dev/null || true
    sleep 2
    
    # Create temporary profile
    local profile_dir="/tmp/test_chrome_profile_$$"
    mkdir -p "$profile_dir"
    
    # Launch Chrome
    local chrome_args=(
        "--user-data-dir=$profile_dir"
        "--remote-debugging-port=9222"
        "--no-first-run"
        "--disable-extensions"
        "--no-sandbox"
        "--headless"
    )
    
    "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" "${chrome_args[@]}" > /dev/null 2>&1 &
    local chrome_pid=$!
    
    # Wait for Chrome to start
    sleep 5
    
    if curl -s "http://localhost:9222/json" > /dev/null 2>&1; then
        log_info "Testing JavaScript execution..."
        
        # Test basic math
        local result=$(curl -s -X POST \
            -H "Content-Type: application/json" \
            -d '{"id":1,"method":"Runtime.evaluate","params":{"expression":"2 + 2","returnByValue":true}}' \
            "http://localhost:9222/json/runtime/evaluate" 2>/dev/null)
        
        if echo "$result" | grep -q '"value":4'; then
            log_success "Basic JavaScript execution works"
        else
            log_error "JavaScript execution failed"
        fi
        
        # Test DOM access
        local dom_result=$(curl -s -X POST \
            -H "Content-Type: application/json" \
            -d '{"id":2,"method":"Runtime.evaluate","params":{"expression":"document.title","returnByValue":true}}' \
            "http://localhost:9222/json/runtime/evaluate" 2>/dev/null)
        
        if [ $? -eq 0 ]; then
            log_success "DOM access works"
        else
            log_error "DOM access failed"
        fi
    else
        log_error "Chrome remote debugging not accessible"
    fi
    
    # Cleanup
    kill $chrome_pid 2>/dev/null || true
    rm -rf "$profile_dir"
}

# Test navigation to Cursor page
test_cursor_navigation() {
    echo -e "\n${CYAN}=== Testing Cursor Page Navigation ===${NC}"
    
    # Kill any existing Chrome processes
    pkill -f "chrome.*--remote-debugging-port=9222" 2>/dev/null || true
    sleep 2
    
    # Create temporary profile
    local profile_dir="/tmp/test_chrome_profile_$$"
    mkdir -p "$profile_dir"
    
    # Launch Chrome
    local chrome_args=(
        "--user-data-dir=$profile_dir"
        "--remote-debugging-port=9222"
        "--no-first-run"
        "--disable-extensions"
        "--no-sandbox"
        "--window-size=1200,800"
    )
    
    log_info "Launching Chrome for Cursor page test..."
    "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" "${chrome_args[@]}" > /dev/null 2>&1 &
    local chrome_pid=$!
    
    # Wait for Chrome to start
    sleep 5
    
    if curl -s "http://localhost:9222/json" > /dev/null 2>&1; then
        log_info "Attempting to navigate to Cursor registration page..."
        
        # Navigate to Cursor page
        local nav_result=$(curl -s -X POST \
            -H "Content-Type: application/json" \
            -d '{"id":3,"method":"Page.navigate","params":{"url":"https://authenticator.cursor.sh/sign-up"}}' \
            "http://localhost:9222/json/runtime/evaluate" 2>/dev/null)
        
        # Wait for page load
        sleep 10
        
        # Check current URL
        local url_result=$(curl -s -X POST \
            -H "Content-Type: application/json" \
            -d '{"id":4,"method":"Runtime.evaluate","params":{"expression":"window.location.href","returnByValue":true}}' \
            "http://localhost:9222/json/runtime/evaluate" 2>/dev/null)
        
        if echo "$url_result" | grep -q "cursor.sh"; then
            log_success "Successfully navigated to Cursor page"
            
            # Check for form elements
            local form_check=$(curl -s -X POST \
                -H "Content-Type: application/json" \
                -d '{"id":5,"method":"Runtime.evaluate","params":{"expression":"document.querySelectorAll(\"input\").length","returnByValue":true}}' \
                "http://localhost:9222/json/runtime/evaluate" 2>/dev/null)
            
            local input_count=$(echo "$form_check" | grep -o '"value":[0-9]*' | cut -d':' -f2)
            if [ -n "$input_count" ] && [ "$input_count" -gt 0 ]; then
                log_success "Found $input_count input fields on the page"
                
                # Get page title
                local title_result=$(curl -s -X POST \
                    -H "Content-Type: application/json" \
                    -d '{"id":6,"method":"Runtime.evaluate","params":{"expression":"document.title","returnByValue":true}}' \
                    "http://localhost:9222/json/runtime/evaluate" 2>/dev/null)
                
                local page_title=$(echo "$title_result" | grep -o '"value":"[^"]*"' | cut -d'"' -f4)
                log_info "Page title: $page_title"
                
            else
                log_warn "No input fields found on the page"
            fi
        else
            log_error "Failed to navigate to Cursor page"
            log_info "Current URL: $(echo "$url_result" | grep -o '"value":"[^"]*"' | cut -d'"' -f4)"
        fi
    else
        log_error "Chrome remote debugging not accessible"
    fi
    
    # Cleanup
    kill $chrome_pid 2>/dev/null || true
    rm -rf "$profile_dir"
}

# Test email generation
test_email_generation() {
    echo -e "\n${CYAN}=== Testing Email Generation ===${NC}"
    
    # Test random email generation
    local domains=("gmail.com" "yahoo.com" "outlook.com")
    local domain=${domains[$RANDOM % ${#domains[@]}]}
    local prefix=$(openssl rand -hex 6)
    local test_email="${prefix}@${domain}"
    
    if [[ "$test_email" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
        log_success "Email generation works: $test_email"
    else
        log_error "Email generation failed: $test_email"
    fi
    
    # Test temporary email services
    log_info "Testing temporary email services..."
    
    if command -v curl >/dev/null 2>&1; then
        # Test guerrillamail
        local guerrilla_test=$(curl -s --connect-timeout 5 "https://www.guerrillamail.com" 2>/dev/null)
        if [ $? -eq 0 ]; then
            log_success "Guerrillamail service accessible"
        else
            log_warn "Guerrillamail service not accessible"
        fi
        
        # Test 10minutemail
        local tenmin_test=$(curl -s --connect-timeout 5 "https://10minutemail.com" 2>/dev/null)
        if [ $? -eq 0 ]; then
            log_success "10minutemail service accessible"
        else
            log_warn "10minutemail service not accessible"
        fi
    else
        log_error "curl not available for testing email services"
    fi
}

# Main test function
main() {
    echo -e "${CYAN}========================================${NC}"
    echo -e "${CYAN}    Simple Browser Automation Test     ${NC}"
    echo -e "${CYAN}========================================${NC}"
    
    local tests_passed=0
    local tests_total=5
    
    # Run tests
    if test_chrome; then ((tests_passed++)); fi
    if test_chrome_launch; then ((tests_passed++)); fi
    if test_javascript; then ((tests_passed++)); fi
    if test_cursor_navigation; then ((tests_passed++)); fi
    if test_email_generation; then ((tests_passed++)); fi
    
    # Show results
    echo -e "\n${CYAN}========================================${NC}"
    echo -e "${CYAN}              Test Results              ${NC}"
    echo -e "${CYAN}========================================${NC}"
    echo -e "${GREEN}Passed: $tests_passed/$tests_total${NC}"
    
    if [ $tests_passed -eq $tests_total ]; then
        echo -e "\n${GREEN}🎉 All tests passed!${NC}"
        echo -e "${GREEN}Browser automation should work correctly.${NC}"
        exit 0
    else
        echo -e "\n${YELLOW}⚠️  Some tests failed.${NC}"
        echo -e "${YELLOW}Browser automation may have issues.${NC}"
        exit 1
    fi
}

main "$@"
