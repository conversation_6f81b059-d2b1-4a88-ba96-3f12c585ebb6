#!/usr/bin/env bash

# Test Script for Enhanced Cursor Reset Tool
# This script tests individual components of the enhanced reset functionality

set -euo pipefail

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Test counter
TEST_COUNT=0
PASS_COUNT=0
FAIL_COUNT=0

# Test functions
test_start() {
    ((TEST_COUNT++))
    echo -e "\n${CYAN}[TEST $TEST_COUNT]${NC} $1"
}

test_pass() {
    ((PASS_COUNT++))
    echo -e "${GREEN}✓ PASS${NC} $1"
}

test_fail() {
    ((FAIL_COUNT++))
    echo -e "${RED}✗ FAIL${NC} $1"
}

test_info() {
    echo -e "${BLUE}ℹ INFO${NC} $1"
}

# Source the enhanced script functions
source_enhanced_script() {
    if [ -f "5Cursor_resetgpt.sh" ]; then
        # Source only the functions we need for testing
        source <(grep -A 1000 "^generate_random_credentials()" 5Cursor_resetgpt.sh | grep -B 1000 "^main()" | head -n -1)
        test_pass "Enhanced script functions loaded"
    else
        test_fail "Enhanced script not found"
        exit 1
    fi
}

# Test credential generation
test_credential_generation() {
    test_start "Testing credential generation"
    
    local credentials=$(generate_random_credentials)
    if [[ "$credentials" =~ ^[A-Za-z]+:[A-Za-z]+:[0-9]+$ ]]; then
        test_pass "Credentials generated in correct format: $credentials"
    else
        test_fail "Invalid credential format: $credentials"
    fi
    
    # Test password generation
    local password=$(generate_secure_password 16)
    if [ ${#password} -eq 16 ]; then
        test_pass "Password generated with correct length"
    else
        test_fail "Password length incorrect: ${#password}"
    fi
    
    # Test enhanced UUID generation
    local uuid=$(generate_enhanced_uuid)
    if [[ "$uuid" =~ ^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[0-9a-f]{4}-[0-9a-f]{12}$ ]]; then
        test_pass "Enhanced UUID generated correctly: $uuid"
    else
        test_fail "Invalid UUID format: $uuid"
    fi
}

# Test email generation
test_email_generation() {
    test_start "Testing temporary email generation"
    
    local email=$(get_temporary_email)
    if [[ "$email" =~ ^[a-zA-Z0-9]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
        test_pass "Email generated in correct format: $email"
    else
        test_fail "Invalid email format: $email"
    fi
}

# Test account database functions
test_account_database() {
    test_start "Testing account database functionality"
    
    # Create temporary database for testing
    local test_db="/tmp/test_cursor_accounts.json"
    ACCOUNT_DB="$test_db"
    
    # Initialize database
    initialize_account_db
    if [ -f "$test_db" ]; then
        test_pass "Account database initialized"
    else
        test_fail "Account database not created"
        return
    fi
    
    # Test saving account
    save_account_to_db "<EMAIL>" "testpass123" "John" "Doe"
    
    if command -v jq >/dev/null 2>&1; then
        local account_count=$(jq '.accounts | length' "$test_db")
        if [ "$account_count" -eq 1 ]; then
            test_pass "Account saved to database"
        else
            test_fail "Account not saved correctly"
        fi
        
        # Test retrieving account
        local account_info=$(get_next_account)
        if [[ "$account_info" == "<EMAIL>:testpass123:John:Doe" ]]; then
            test_pass "Account retrieved correctly"
        else
            test_fail "Account retrieval failed: $account_info"
        fi
    else
        test_info "jq not available, skipping JSON database tests"
    fi
    
    # Cleanup
    rm -f "$test_db"
}

# Test hardware identifier randomization
test_hardware_randomization() {
    test_start "Testing hardware identifier randomization"
    
    # Test UUID generation uniqueness
    local uuid1=$(generate_enhanced_uuid)
    local uuid2=$(generate_enhanced_uuid)
    
    if [ "$uuid1" != "$uuid2" ]; then
        test_pass "UUIDs are unique"
    else
        test_fail "UUIDs are not unique"
    fi
    
    # Test randomization function
    if randomize_hardware_identifiers; then
        test_pass "Hardware identifier randomization completed"
    else
        test_fail "Hardware identifier randomization failed"
    fi
}

# Test browser automation setup
test_browser_setup() {
    test_start "Testing browser automation setup"
    
    # Check if Chrome is available
    if [ -f "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" ]; then
        test_pass "Google Chrome found"
        
        # Test profile directory creation
        local profile_dir=$(setup_browser_automation)
        if [ -d "$profile_dir" ]; then
            test_pass "Browser profile directory created: $profile_dir"
            rm -rf "$profile_dir"
        else
            test_fail "Browser profile directory not created"
        fi
    else
        test_info "Google Chrome not found, skipping browser tests"
    fi
}

# Test storage modification functions
test_storage_functions() {
    test_start "Testing storage modification functions"
    
    # Create temporary storage file for testing
    local test_storage="/tmp/test_storage.json"
    cat > "$test_storage" << 'EOF'
{
    "telemetry.devDeviceId": "old-device-id",
    "telemetry.machineId": "old-machine-id",
    "telemetry.macMachineId": "old-mac-id"
}
EOF
    
    # Backup original path and set test path
    local original_storage="$STORAGE_JSON"
    STORAGE_JSON="$test_storage"
    
    # Test modification
    if enhanced_modify_storage; then
        test_pass "Storage modification completed"
        
        # Verify changes were made
        if command -v jq >/dev/null 2>&1; then
            local new_device_id=$(jq -r '.["telemetry.devDeviceId"]' "$test_storage")
            if [ "$new_device_id" != "old-device-id" ]; then
                test_pass "Device ID was changed"
            else
                test_fail "Device ID was not changed"
            fi
        fi
    else
        test_fail "Storage modification failed"
    fi
    
    # Restore original path and cleanup
    STORAGE_JSON="$original_storage"
    rm -f "$test_storage"
}

# Test verification code simulation
test_verification_simulation() {
    test_start "Testing email verification simulation"
    
    local verification_code=$(check_email_verification "<EMAIL>")
    if [[ "$verification_code" =~ ^[0-9]{6}$ ]]; then
        test_pass "Verification code generated: $verification_code"
    else
        test_fail "Invalid verification code: $verification_code"
    fi
}

# Test network identifier functions
test_network_functions() {
    test_start "Testing network identifier randomization"
    
    if randomize_network_identifiers; then
        test_pass "Network identifier randomization completed"
    else
        test_fail "Network identifier randomization failed"
    fi
}

# Test logging functions
test_logging() {
    test_start "Testing logging functionality"
    
    # Initialize log
    initialize_log
    
    if [ -f "$LOG_FILE" ]; then
        test_pass "Log file created: $LOG_FILE"
        
        # Test different log levels
        log_info "Test info message"
        log_warn "Test warning message"
        log_error "Test error message"
        log_success "Test success message"
        
        # Check if messages were logged
        if grep -q "Test info message" "$LOG_FILE"; then
            test_pass "Log messages written correctly"
        else
            test_fail "Log messages not written"
        fi
    else
        test_fail "Log file not created"
    fi
}

# Main test execution
main() {
    echo -e "${CYAN}========================================${NC}"
    echo -e "${CYAN}  Enhanced Cursor Reset Tool - Tests   ${NC}"
    echo -e "${CYAN}========================================${NC}"
    
    # Source the enhanced script
    source_enhanced_script
    
    # Run all tests
    test_credential_generation
    test_email_generation
    test_account_database
    test_hardware_randomization
    test_browser_setup
    test_storage_functions
    test_verification_simulation
    test_network_functions
    test_logging
    
    # Show results
    echo -e "\n${CYAN}========================================${NC}"
    echo -e "${CYAN}              Test Results              ${NC}"
    echo -e "${CYAN}========================================${NC}"
    echo -e "${GREEN}Passed: $PASS_COUNT${NC}"
    echo -e "${RED}Failed: $FAIL_COUNT${NC}"
    echo -e "${BLUE}Total:  $TEST_COUNT${NC}"
    
    if [ $FAIL_COUNT -eq 0 ]; then
        echo -e "\n${GREEN}🎉 All tests passed!${NC}"
        exit 0
    else
        echo -e "\n${YELLOW}⚠️  Some tests failed. Please review the output above.${NC}"
        exit 1
    fi
}

main "$@"
