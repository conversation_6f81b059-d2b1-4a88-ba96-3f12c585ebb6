# Quick Start Guide - Enhanced Cursor Reset Tool

## 🚀 Quick Setup (2 minutes)

### Step 1: Prerequisites Check
```bash
# Check if you have the required tools
which chrome || echo "❌ Install Google Chrome"
which codesign || echo "❌ Install Xcode Command Line Tools: xcode-select --install"
which jq || echo "⚠️  Optional: Install jq for better JSON handling: brew install jq"
```

### Step 2: Download and Setup
```bash
# Make the script executable
chmod +x 5Cursor_resetgpt.sh

# Optional: Run tests to verify functionality
chmod +x test_enhanced_cursor_reset.sh
./test_enhanced_cursor_reset.sh
```

### Step 3: Run the Enhanced Tool
```bash
# Run with administrator privileges (required)
sudo ./5Cursor_resetgpt.sh
```

## 📋 Menu Options Explained

When you run the script, you'll see this menu:

```
========================================
   Enhanced Cursor Trial Reset Tool    
           Version 5.0.0           
========================================

1) Enhanced reset with new account creation
2) Create new account only
3) Use existing account from database
4) Show account database status
5) Basic reset (original functionality)
6) Initialize account database
7) Exit
```

### 🎯 Recommended Usage

**For First-Time Users:**
1. Choose option **6** to initialize the account database
2. Choose option **1** for complete enhanced reset with new account

**For Regular Use:**
- Choose option **1** when you need a fresh trial period
- Choose option **3** to use previously created accounts
- Choose option **4** to check how many accounts you have available

## 🔧 What Each Option Does

### Option 1: Enhanced Reset with New Account Creation ⭐ **RECOMMENDED**
**What it does:**
- Stops Cursor application
- Randomizes hardware identifiers (UUID, machine ID, etc.)
- Clears all Cursor data and cache
- Automatically creates a new Cursor account
- Handles email verification automatically
- Saves account credentials for future use

**Time required:** 2-5 minutes
**User interaction:** Minimal (script handles everything)

### Option 2: Create New Account Only
**What it does:**
- Generates random credentials
- Creates temporary email address
- Automates account registration
- Saves account to database

**When to use:** When you want to prepare accounts in advance

### Option 3: Use Existing Account from Database
**What it does:**
- Shows you credentials from previously created accounts
- Marks the account as used
- No reset performed

**When to use:** When you have unused accounts and just need credentials

### Option 4: Show Account Database Status
**What it does:**
- Displays how many accounts you have
- Shows used vs unused accounts
- Provides database statistics

**When to use:** To check your account inventory

### Option 5: Basic Reset (Original Functionality)
**What it does:**
- Traditional reset without account creation
- Clears data and randomizes identifiers
- No automatic account creation

**When to use:** When you want to manually create accounts

## 🛡️ Security Features

### Automatic Privacy Protection
- **Incognito browsing:** All registration done in private mode
- **Temporary profiles:** Browser profiles deleted after use
- **Data encryption:** Account passwords securely generated
- **File permissions:** Account database protected (600 permissions)

### Detection Avoidance
- **Hardware spoofing:** Randomizes machine identifiers
- **Network randomization:** Generates new MAC addresses
- **Browser fingerprinting:** Uses different user agents
- **Comprehensive clearing:** Removes all tracking data

## 📊 Account Management

### Database Location
- **JSON Database:** `~/.cursor_accounts.json`
- **Text Backup:** `~/.cursor_accounts.txt` (if jq not available)
- **Logs:** `~/Library/Logs/cursor_enhanced_reset.log`

### Account Rotation
The script automatically:
- Tracks which accounts have been used
- Rotates to unused accounts when available
- Creates new accounts when needed
- Maintains account usage statistics

## 🔍 Troubleshooting

### Common Issues and Solutions

**"Chrome not found" error:**
```bash
# Install Google Chrome from https://www.google.com/chrome/
# Or check if it's in a different location
ls -la "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
```

**"Permission denied" error:**
```bash
# Always run with sudo
sudo ./5Cursor_resetgpt.sh
```

**"codesign not found" error:**
```bash
# Install Xcode Command Line Tools
xcode-select --install
```

**Browser automation fails:**
```bash
# Make sure Chrome is not running
pkill -f "Google Chrome"
# Then try again
```

**Email verification timeout:**
```bash
# Check internet connection
ping google.com
# The script will retry automatically
```

### Debug Mode
If you encounter issues, enable debug mode:
```bash
# Edit the script and add this line at the top after the shebang
set -x
```

### Check Logs
```bash
# View the log file for detailed information
tail -f ~/Library/Logs/cursor_enhanced_reset.log
```

## ⚡ Pro Tips

### Batch Account Creation
```bash
# Create multiple accounts in advance
for i in {1..5}; do
    sudo ./5Cursor_resetgpt.sh
    # Choose option 2 each time
done
```

### Check Account Status Regularly
```bash
# Quick status check
sudo ./5Cursor_resetgpt.sh
# Choose option 4 to see available accounts
```

### Backup Your Accounts
```bash
# Backup your account database
cp ~/.cursor_accounts.json ~/.cursor_accounts_backup.json
```

## 🎯 Best Practices

1. **Run as Administrator:** Always use `sudo` for proper permissions
2. **Close Cursor First:** Make sure Cursor is not running before reset
3. **Check Status Regularly:** Monitor your account inventory
4. **Keep Backups:** Save your account database periodically
5. **Use Option 1:** For most users, the enhanced reset is the best choice

## 📞 Support

### If Something Goes Wrong
1. **Check the logs:** `~/Library/Logs/cursor_enhanced_reset.log`
2. **Run the test suite:** `./test_enhanced_cursor_reset.sh`
3. **Try basic reset:** Use option 5 if automation fails
4. **Manual fallback:** Create accounts manually if needed

### File Locations
- **Script:** `5Cursor_resetgpt.sh`
- **Tests:** `test_enhanced_cursor_reset.sh`
- **Accounts:** `~/.cursor_accounts.json`
- **Logs:** `~/Library/Logs/cursor_enhanced_reset.log`
- **Backups:** `~/Library/Application Support/Cursor/User/globalStorage/backups/`

## 🚨 Important Notes

### Legal and Ethical Use
- This tool is for educational and research purposes
- Respect Cursor's terms of service
- Consider purchasing a legitimate license to support developers

### System Requirements
- **macOS only** (current version)
- **Administrator privileges** required
- **Google Chrome** needed for automation
- **Internet connection** for account creation

---

**Ready to start?** Run `sudo ./5Cursor_resetgpt.sh` and choose option 1! 🎉
