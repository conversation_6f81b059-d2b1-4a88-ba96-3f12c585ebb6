#!/bin/bash

# macOS Cursor Application Reset and Modification Script (Updated Version)
#
# Important Notes:
# - This script aims to modify Cursor application internal files to change device identification logic.
# - Modifying the application may violate Cursor's Terms of Service. Use at your own risk.
# - The script's success rate is highly dependent on the specific Cursor version; updates may break the script.
# - It is recommended to back up important data before execution.
# - Purchasing a legitimate license to support the developers is recommended.

# Set error handling: exit script if any command fails
set -e

# Define log file path (use safer /var/log or user directory)
# Note: Writing to /var/log requires root privileges, feasible if run with sudo.
# Alternatively, use $HOME/Library/Logs/
LOG_DIR="$HOME/Library/Logs"
mkdir -p "$LOG_DIR" # Ensure directory exists
LOG_FILE="$LOG_DIR/cursor_mac_modifier.log"

# Initialize log file
initialize_log() {
    echo "========== Cursor Modifier Log Start $(date) ==========" > "$LOG_FILE"
    # Set permissions, ensure current user can read/write
    chmod 644 "$LOG_FILE"
    # If run with sudo, log file owner might be root; change to current user
    if [ "$EUID" -eq 0 ] && [ -n "$SUDO_USER" ]; then
        chown "$SUDO_USER" "$LOG_FILE"
    fi
    echo "Log file located at: $LOG_FILE"
}

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Base logging function
_log_base() {
    local color_code="$1"
    local level_text="$2"
    local message="$3"
    echo -e "${color_code}[${level_text}]${NC} ${message}"
    # Ensure log entry format is consistent, remove echo -e color codes
    local log_message=$(echo -e "${message}" | sed 's/\x1b\[[0-9;]*m//g')
    echo "[${level_text}] $(date '+%Y-%m-%d %H:%M:%S') ${log_message}" >> "$LOG_FILE"
}

log_info() { _log_base "$GREEN" "INFO" "$1"; }
log_warn() { _log_base "$YELLOW" "WARN" "$1"; }
log_error() { _log_base "$RED" "ERROR" "$1"; }
log_debug() { _log_base "$BLUE" "DEBUG" "$1"; }

# Log command output to log file (enhanced, captures stderr)
log_cmd_output() {
    local cmd="$1"
    local msg="$2"
    log_debug "Executing command: $cmd ($msg)"
    echo "[CMD] $(date '+%Y-%m-%d %H:%M:%S') Executing command: $cmd ($msg)" >> "$LOG_FILE"
    # Use eval to execute command, redirect stdout and stderr to log and terminal
    # Using a temporary file for combined output to avoid complex redirection issues with tee inside eval
    local tmp_output
    tmp_output=$(mktemp)
    # Capture exit status correctly with process substitution and tee
    # Using a subshell and checking $? immediately after avoids issues with pipelines and set -e
    local exit_code=0
    ( eval "$cmd" 2> >(tee -a "$tmp_output" >&2) > >(tee -a "$tmp_output") ) || exit_code=$?
    cat "$tmp_output" >> "$LOG_FILE"
    rm "$tmp_output"
    echo " [CMD] $(date '+%Y-%m-%d %H:%M:%S') Command exit code: $exit_code" >> "$LOG_FILE"
    echo "" >> "$LOG_FILE"
    # Return the captured exit code
    return $exit_code
}


# Get current user (handle sudo case)
get_current_user() {
    # If root user (EUID=0) and SUDO_USER variable exists, use SUDO_USER
    if [ "$EUID" -eq 0 ] && [ -n "$SUDO_USER" ]; then
        echo "$SUDO_USER"
    else
        # Otherwise use current USER
        echo "$USER"
    fi
}

CURRENT_USER=$(get_current_user)
if [ -z "$CURRENT_USER" ]; then
    # Output error directly to stderr and exit, as logging might not be initialized
    echo -e "${RED}${NC} Cannot get a valid current username." >&2
    exit 1
fi
# Get current user's HOME directory, correct even under sudo
CURRENT_USER_HOME=$(eval echo "~$CURRENT_USER")

# Define config file path and backup directory (use $CURRENT_USER_HOME for correctness)
CURSOR_APP_SUPPORT_DIR="$CURRENT_USER_HOME/Library/Application Support/Cursor"
STORAGE_FILE="$CURSOR_APP_SUPPORT_DIR/User/globalStorage/storage.json"
BACKUP_DIR="$CURSOR_APP_SUPPORT_DIR/User/globalStorage/backups"

# Define Cursor application path
CURSOR_APP_PATH="/Applications/Cursor.app"

# Check permissions (must run with sudo)
check_permissions() {
    if [ "$EUID" -ne 0 ]; then
        log_error "This script requires administrator privileges to modify application files and system settings."
        echo "Please run using 'sudo $0'."
        exit 1
    fi
    log_info "Permission check passed (running as root or with sudo). Operations will affect user: $CURRENT_USER"
}

# Check and close Cursor process (enhanced with pkill)
check_and_kill_cursor() {
    log_info "Checking for and closing Cursor processes..."
    local attempt=1
    local max_attempts=5

    while [ $attempt -le $max_attempts ]; do
        # Use pkill -f more reliably to find processes containing "Cursor.app" path
        # -i case-insensitive, -f match full command line
        # Get PID list
        CURSOR_PIDS=$(pgrep -if "Cursor.app" || true) # || true prevents pgrep non-zero exit from stopping script via set -e

        if [ -z "$CURSOR_PIDS" ]; then
            log_info "No running Cursor processes found."
            return 0
        fi

        log_warn "Found running Cursor processes (PIDs: $CURSOR_PIDS)."
        log_debug "Process details:"
        # Log process details, ignore error if process disappears
        (ps -p $CURSOR_PIDS || log_warn "Could not get details for some processes (may have exited)") >> "$LOG_FILE"


        log_warn "Attempting to gracefully shut down Cursor processes (Attempt $attempt/$max_attempts)..."
        # Try sending TERM signal (graceful exit)
        # Use xargs to handle multiple PIDs, ignore errors if process is gone
        echo "$CURSOR_PIDS" | xargs kill -TERM 2>/dev/null || true

        sleep 2 # Wait for processes to respond

        # Check again
        CURSOR_PIDS_AFTER_TERM=$(pgrep -if "Cursor.app" || true)

        if [ -z "$CURSOR_PIDS_AFTER_TERM" ]; then
            log_info "Cursor processes closed successfully."
            return 0
        fi

        # If still running and it's the last attempt, force kill
        if [ $attempt -eq $max_attempts ]; then
            log_warn "Graceful shutdown failed, attempting force termination (KILL)..."
            echo "$CURSOR_PIDS_AFTER_TERM" | xargs kill -KILL 2>/dev/null || true
            sleep 1 # Short wait for force kill to take effect
            # Final check
            if pgrep -if "Cursor.app" > /dev/null; then
                log_error "Cursor processes remain even after force termination. Please close them manually and retry."
                ps aux | grep -i "Cursor.app" | grep -v grep >> "$LOG_FILE"
                exit 1
            else
                log_info "Cursor processes have been force closed."
                return 0
            fi
        fi

        log_warn "Processes still running, waiting and retrying..."
        ((attempt++))
        sleep 1
    done

    log_error "Failed to close all Cursor processes after $max_attempts attempts."
    log_error "Please manually close all Cursor processes and re-run the script."
    exit 1
}

# Backup configuration file
backup_config() {
    log_info "Checking and backing up config file $STORAGE_FILE..."
    if [ ! -f "$STORAGE_FILE" ]; then # Corrected: Added file check condition
        log_warn "Configuration file does not exist, skipping backup."
        return 0
    fi

    # Ensure backup directory exists, and set correct ownership and permissions
    mkdir -p "$BACKUP_DIR"
    # Setting ownership requires sudo if the script wasn't started by the user
    sudo chown "$CURRENT_USER" "$BACKUP_DIR"
    sudo chmod 755 "$BACKUP_DIR"

    local backup_file="$BACKUP_DIR/storage.json.backup_$(date +%Y%m%d_%H%M%S)"

    log_info "Backing up to: $backup_file"
    # Use sudo for cp in case permissions are restrictive, then fix ownership
    if sudo cp "$STORAGE_FILE" "$backup_file"; then
        # Ensure backup file permissions and ownership are correct
        sudo chmod 644 "$backup_file"
        sudo chown "$CURRENT_USER" "$backup_file"
        log_info "Configuration file backup successful."
    else
        log_error "Failed to back up configuration file!"
        # Don't exit, but log severe error
        return 1
    fi
    return 0
}

# Generate random UUID (keep as is)
generate_uuid() {
    uuidgen | tr '[:upper:]' '[:lower:]'
}

# !! Function to modify or add config file (Currently unused in script, retained but marked) !!
# Note: This function was not called in the original script's main logic to modify the ID.
# If JSON modification is needed again, using jq or a more robust method is recommended.
modify_or_add_config_json() {
    local key="$1"
    local value="$2"
    local file="$3"

    log_warn "Function 'modify_or_add_config_json' was called, but the current script primarily relies on JS patching."
    log_warn "If JSON modification is needed, using jq is recommended."

    if ! command -v jq &> /dev/null; then
        log_error "'jq' command not found. Cannot safely modify JSON file. Please install jq (brew install jq)."
        return 1
    fi

    if [ ! -f "$file" ]; then # Corrected: Added space after [
        log_error "JSON file does not exist: $file"
        return 1
    fi

    # Ensure file permissions allow the target user to write (even if run with sudo, operate on user files)
    sudo chown "$CURRENT_USER" "$file"
    sudo chmod 644 "$file"

    local temp_file
    temp_file=$(mktemp) # Create temporary file

    # Use jq to modify or add key-value pair
    # '.' represents the entire JSON object
    # '|=.' represents updating the object
    # "+ {$key: $value}" represents adding or overwriting the key-value pair
    # Run jq as the target user to handle permissions correctly if possible, or use sudo if needed
    if sudo -u "$CURRENT_USER" jq --arg k "$key" --arg v "$value" '.[$k] = $v' "$file" > "$temp_file"; then
        # Validate if jq operation succeeded and generated a non-empty file
        if [ -s "$temp_file" ]; then
            # Overwrite original file with modified content (as root, then fix perms)
            cat "$temp_file" | sudo tee "$file" > /dev/null
            rm "$temp_file"
            # Restore ownership and permissions (if read-only needed, change here to 444)
            sudo chown "$CURRENT_USER" "$file"
            sudo chmod 644 "$file" # Keep writable, or change to 444 as needed
            log_info "Successfully used jq to modify/add key '$key' to file $file"
            return 0
        else
            log_error "Temporary file generated by jq is empty: $temp_file"
            rm "$temp_file"
            return 1
        fi
    else
        log_error "Failed to modify JSON file using jq: $file"
        rm "$temp_file"
        return 1
    fi
}

# Configuration file processing (explicitly state ID is not modified)
process_config_file() {
    log_info "Processing configuration file $STORAGE_FILE..."

    if [ -f "$STORAGE_FILE" ]; then # Corrected: Added file check condition
        log_info "Existing configuration file found, performing backup."
        backup_config
        # Explicitly not calling modify_or_add_config_json to change the ID here
        log_info "Script currently resets device identification primarily by modifying JS files, skipping ID modification in storage.json."
    else
        log_warn "Configuration file not found: $STORAGE_FILE. This might be the first run or app data was cleared."
        log_info "Skipping configuration file processing."
    fi
    log_info "Configuration file processing complete."
}

# Cleanly restore Cursor application from previous modification (restore backup)
restore_original_app() {
    log_info "Attempting to restore Cursor application to its original backed-up state..."

    # Find the latest application backup directory (Assume backup is in a persistent location)
    local app_backup_base_dir="$CURSOR_APP_SUPPORT_DIR/backups/app_backups" # Using the persistent location
    local latest_backup
    # Find directories named Cursor.app.backup_* within the base dir, sort reverse (latest first), take the first one
    latest_backup=$(find "$app_backup_base_dir" -name "Cursor.app.backup_*" -type d -maxdepth 1 -print 2>/dev/null | sort -r | head -n 1)


    if [ -n "$latest_backup" ] && [ -d "$latest_backup" ]; then
        log_info "Found latest application backup: $latest_backup"
        log_info "Preparing to restore..."

        # Ensure Cursor is closed again
        check_and_kill_cursor

        log_info "Removing current application..."
        # Use sudo to remove and copy
        if sudo rm -rf "$CURSOR_APP_PATH"; then
            log_info "Current application removed."
        else
            log_error "Failed to remove current application: $CURSOR_APP_PATH"
            return 1
        fi

        log_info "Restoring from backup..."
        if sudo cp -a "$latest_backup" "$CURSOR_APP_PATH"; then # Use -a to preserve attributes
            log_info "Application restored from backup."
            # May need to reset permissions and ownership after restore
            sudo chown -R root:wheel "$CURSOR_APP_PATH" # Standard app ownership
            sudo chmod -R 755 "$CURSOR_APP_PATH"
            log_info "Restored application permissions."
            # May need to handle quarantine attribute after restore
            log_info "Attempting to remove quarantine attribute from restored application..."
            # Ignore error if attribute doesn't exist
            # Corrected: Use || for error handling, not | |
            (sudo xattr -rd com.apple.quarantine "$CURSOR_APP_PATH" 2>/dev/null) || log_warn "Error removing quarantine attribute or attribute not present."
            log_info "Restore complete. If you previously disabled auto-updates, you might need to disable them again."
            return 0
        else
            log_error "Failed to restore application from backup! Source: $latest_backup, Target: $CURSOR_APP_PATH"
            log_error "System might be in an inconsistent state! Recommend manually restoring from backup or reinstalling Cursor."
            return 1
        fi
    else
        log_warn "No usable Cursor application backup directory found (looked for Cursor.app.backup_* in $app_backup_base_dir)."
        log_warn "Cannot automatically restore. Please reinstall Cursor if you need the original version."
        return 1 # Return non-zero indicates not restored
    fi
}


# Modify Cursor main application files (safe mode, enhanced)
modify_cursor_app_files() {
    log_info "Starting safe modification of Cursor main application files..."
    log_info "Target application: $CURSOR_APP_PATH"
    log_info "Detailed logs recorded in: $LOG_FILE"

    # 1. Verify application exists
    if [ ! -d "$CURSOR_APP_PATH" ]; then # Corrected: Added directory check condition
        log_error "Cursor application not found. Please verify installation path: $CURSOR_APP_PATH"
        return 1
    fi
    log_info "Cursor application exists."

    # 2. Define target JS files (order by priority)
    local target_files=(
        "$CURSOR_APP_PATH/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js"
        "$CURSOR_APP_PATH/Contents/Resources/app/out/main.js"
        "$CURSOR_APP_PATH/Contents/Resources/app/out/vs/code/node/cliProcessMain.js"
        # Add other files potentially containing device ID logic as needed
    )

    # 3. Check if files exist and if modification is needed (avoid duplicate operations)
    local all_files_exist=true
    local needs_modification=false
    log_info "Checking status of target JS files..."
    for file in "${target_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_warn "Target file does not exist: ${file#"$CURSOR_APP_PATH/"}" # Show relative path
            all_files_exist=false
        else
            log_debug "File exists: ${file#"$CURSOR_APP_PATH/"}"
            # Check if it already contains our injected signature code (e.g., 'crypto.randomUUID()' or specific comment)
            # Note: This check logic needs to correspond to the actual modification logic below
            # Corrected || logic instead of | |
            if grep -q "crypto.randomUUID()" "$file" || grep -q "Cursor ID Modifier Injected" "$file" || grep -q 'x-cursor-checksum.*p}${t}/${p}`' "$file"; then
                log_info "File seems to have been modified already: ${file#"$CURSOR_APP_PATH/"}"
            else
                log_info "File needs modification: ${file#"$CURSOR_APP_PATH/"}"
                needs_modification=true
            fi
        fi
    done

    if [ "$all_files_exist" = false ]; then
        log_error "Some target JS files do not exist. Please verify Cursor installation integrity or version compatibility."
        return 1
    fi

    if [ "<span class="math-inline">needs\_modification" \= false \]; then
log\_info "All target files seem to have been modified already, no need for repeat operations\."
return 0 \# Success exit, as no modification needed
fi
\# 4\. Create temporary working environment and backup
local timestamp
timestamp\=</span>(date +%Y%m%d_%H%M%S)
    local temp_dir="/tmp/cursor_modify_${timestamp}"
    local temp_app_path="${temp_dir}/Cursor.app"
    # Place app backup in a more persistent location, not /tmp
    local app_backup_dir="$CURSOR_APP_SUPPORT_DIR/backups/app_backups"
    mkdir -p "$app_backup_dir" || { log_error "Failed to create persistent backup directory: $app_backup_dir"; return 1; }
    sudo chown "$CURRENT_USER" "<span class="math-inline">app\_backup\_dir" \# Ensure user owns the backups parent dir
local backup\_app\_path\="</span>{app_backup_dir}/Cursor.app.backup_${timestamp}"

    log_info "Creating temporary working directory: $temp_dir"
    mkdir -p "$temp_dir" || { log_error "Cannot create temporary directory: $temp_dir"; return 1; }

    log_info "Backing up original application to: $backup_app_path"
    # Use sudo cp -a to preserve permissions and attributes
    if sudo cp -a "$CURSOR_APP_PATH" "$backup_app_path"; then
        log_info "Application backup successful."
    else
        log_error "Failed to back up application! Source: $CURSOR_APP_PATH, Target: $backup_app_path"
        rm -rf "$temp_dir" # Clean up temp directory
        return 1
    fi

    log_info "Copying application to temporary directory for modification..."
    # Use sudo cp -a
    if sudo cp -a "$CURSOR_APP_PATH" "$temp_app_path"; then
        log_info "Application copied to temporary directory."
        # Ensure temporary copy ownership/permissions are correct for subsequent operations
        # Temporarily change to current user:staff for easier modification (though script still uses sudo for sed etc)
        sudo chown -R "$USER:staff" "$temp_app_path"
        sudo chmod -R u+w "$temp_app_path" # Ensure user has write permission
    else
        log_error "Failed to copy application to temporary directory! Source: $CURSOR_APP_PATH, Target: $temp_app_path"
        rm -rf "$temp_dir" # Clean up temp directory
        # Note: backup file backup_app_path still exists
        return 1
    fi

    # 5. Remove signature (operate on the temporary copy)
    log_info "Removing signature from temporary application copy..."
    # Remove main application signature
    if sudo codesign --remove-signature "$temp_app_path" 2>> "$LOG_FILE"; then
        log_info "Main application signature removed."
    else
        log_warn "Failed to remove main application signature or application was not signed."
    fi
    # Remove signatures of helper apps (if they exist)
    local components=(
        "$temp_app_path/Contents/Frameworks/Cursor Helper.app"
        "$temp_app_path/Contents/Frameworks/Cursor Helper (GPU).app"
        "$temp_app_path/Contents/Frameworks/Cursor Helper (Plugin).app"
        "<span class="math-inline">temp\_app\_path/Contents/Frameworks/Cursor Helper \(Renderer\)\.app"
\)
for component in "</span>{components[@]}"; do
        if [ -e "$component" ]; then
            log_debug "Removing component signature: ${component#"$temp_dir/"}"
            # Ignore errors if removing signature fails (might not be signed)
            # Corrected: Use || for error handling
            (sudo codesign --remove-signature "$component" 2>> "$LOG_FILE") || log_warn "Removing signature for component ${component#"<span class="math-inline">temp\_dir/"\} failed or it was not signed\."
fi
done
\# 6\. Modify target JS files \(operate on the temporary copy\)
log\_info "Starting modification of JS files in the temporary copy\.\.\."
local modified\_count\=0
local patch\_successful\=false \# Flag if at least one critical patch was applied
for file in "</span>{target_files[@]}"; do
        local temp_file_path="${file/$CURSOR_APP_PATH/$temp_app_path}" # Construct temporary file path

        if [ ! -f "$temp_file_path" ]; then # Corrected: Added space
            log_warn "Skipping non-existent file: ${temp_file_path#"$temp_dir/"}"
            continue
        fi

        # Check if already modified (check again, just in case)
        # Corrected || logic
        if grep -q "crypto.randomUUID()" "$temp_file_path" || grep -q "Cursor ID Modifier Injected" "<span class="math-inline">temp\_file\_path" \|\| grep \-q 'x\-cursor\-checksum\.\*p\}</span>{t}/${p}`' "$temp_file_path"; then
            log_info "File already modified, skipping: ${temp_file_path#"$temp_dir/"}"
            ((modified_count++)) # Count as modified
            patch_successful=true # Assume previous modification was successful
            continue
        fi

        log_info "Processing file: ${temp_file_path#"$temp_dir/"}"
        # Create file backup (within the temporary directory)
        # Corrected: Use || for error handling
        if ! sudo cp "$temp_file_path" "${temp_file_path}.bak"; then
            log_error "Cannot create temporary backup: ${temp_file_path#"$temp_dir/"}";
            continue; # Skip this file
        fi

        # Ensure file is writable (already done with chmod u+w earlier)
        # sudo chmod u+w "$temp_file_path"

        # Apply patches - using similar logic to original script, but with added verification
        local current_file_patched=false

        # Patch 1: checksum in extensionHostProcess.js (from original script)
        if [[ "$temp_file_path" == *"extensionHostProcess.js"* ]]; then
            log_debug "Attempting to apply checksum patch..."
            # Escape special characters for sed pattern
            local target_pattern='i\.header\.set("x-cursor-checksum",e===void 0?`<span class="math-inline">\{p\}</span>{t}`:`<span class="math-inline">\{p\}</span>{t}\/${e}`)'
            local replacement_pattern='i.header.set("x-cursor-checksum",e===void 0?`<span class="math-inline">\{p\}</span>{t}`:`<span class="math-inline">\{p\}</span>{t}\/${p}`)' # Note: Verify the intent of this replacement (e -> p) carefully
            # Check if pattern exists before attempting sed
            if grep -q "$target_pattern" "<span class="math-inline">temp\_file\_path"; then
\# Use sudo for sed as we are modifying files potentially owned by root initially
if sudo sed \-i\.tmp "s/</span>{target_pattern}/${replacement_pattern}/" "$temp_file_path"; then
                    # Verify replacement was successful
                    if grep -q "$replacement_pattern" "<span class="math-inline">temp\_file\_path"; then
log\_info "Successfully applied checksum patch\."
current\_file\_patched\=true
patch\_successful\=true \# Mark critical patch successful
else
log\_error "Verification failed after applying checksum patch\! Restoring file backup\."
sudo mv "</span>{temp_file_path}.bak" "<span class="math-inline">temp\_file\_path"
fi
else
log\_error "Failed to apply checksum patch using sed\."
sudo mv "</span>{temp_file_path}.bak" "<span class="math-inline">temp\_file\_path"
fi
sudo rm \-f "</span>{temp_file_path}.tmp" # Clean up sed backup
            else
                log_warn "Checksum target pattern not found, skipping this patch."
                # Log relevant lines for debugging, ignore errors
                # Corrected: Use || true for grep/head pipeline
                (grep -n "x-cursor-checksum" "$temp_file_path" | head -5 >> "$LOG_FILE") || true
            fi
        fi

        # Patch 2: Replace IOPlatformUUID related logic with randomUUID (from original script, multiple patterns)
        if grep -q "IOPlatformUUID" "<span class="math-inline">temp\_file\_path"; then
log\_debug "Attempting to apply IOPlatformUUID \(randomUUID\) patch\.\.\."
local patched\_ioplatform\=false
\# Pattern a\: function a</span>
            # Escape $ for grep and sed
            if grep -q 'function a\$(t){switch' "<span class="math-inline">temp\_file\_path"; then
\# Use sudo for sed
if sudo sed \-i\.tmp 's/function a\\$\(t\)\{switch/function a\\</span>(t){return crypto.randomUUID(); \/\* Cursor ID Modifier Injected \*\/ switch/' "$temp_file_path"; then
                    if grep -q 'return crypto.randomUUID(); /\* Cursor ID Modifier Injected \*/ switch' "<span class="math-inline">temp\_file\_path"; then
log\_info "Successfully applied randomUUID patch \(pattern a</span>)."
                        patched_ioplatform=true
                    else
                        log_error "Verification failed after applying randomUUID patch (pattern a$)! Restoring backup."
                        sudo mv "${temp_file_path}.bak" "<span class="math-inline">temp\_file\_path"
fi
else
log\_error "Failed to apply randomUUID patch \(pattern a</span>) using sed."
                    sudo mv "${temp_file_path}.bak" "<span class="math-inline">temp\_file\_path"
fi
sudo rm \-f "</span>{temp_file_path}.tmp"
            fi
            # Pattern b: function v5 (if pattern a was not successful)
            if [ "$patched_ioplatform" = false ] && grep -q 'async function v5(t){let e=' "$temp_file_path"; then
                 # Use sudo for sed
                 if sudo sed -i.tmp 's/async function v5(t){let e=/async function v5(t){return crypto.randomUUID(); \/\* Cursor ID Modifier Injected \*\/ let e=/' "$temp_file_path"; then
                     if grep -q 'return crypto.randomUUID(); /\* Cursor ID Modifier Injected \*/ let e=' "<span class="math-inline">temp\_file\_path"; then
log\_info "Successfully applied randomUUID patch \(pattern v5\)\."
patched\_ioplatform\=true
else
log\_error "Verification failed after applying randomUUID patch \(pattern v5\)\! Restoring backup\."
sudo mv "</span>{temp_file_path}.bak" "<span class="math-inline">temp\_file\_path"
fi
else
log\_error "Failed to apply randomUUID patch \(pattern v5\) using sed\."
sudo mv "</span>{temp_file_path}.bak" "<span class="math-inline">temp\_file\_path"
fi
sudo rm \-f "</span>{temp_file_path}.tmp"
            fi

            if [ "$patched_ioplatform" = true ]; then
                current_file_patched=true
                patch_successful=true # Mark critical patch successful
            else
                # Only warn if IOPlatformUUID was found but no patterns matched
                if grep -q "IOPlatformUUID" "<span class="math-inline">temp\_file\_path"; then \# Check again to be sure it wasn't missed earlier
log\_warn "Found IOPlatformUUID but failed to match known patterns \(a</span>, v5) for replacement."
                   # Log relevant lines, ignore errors
                   # Corrected: Use || true
                   (grep -n "IOPlatformUUID" "$temp_file_path" | head -5 >> "$LOG_FILE") || true
                fi
            fi
        else
             log_debug "Keyword 'IOPlatformUUID' not found in file."
        fi

        # Patch 3: Generic injection (if specific patches above were not applied) - Use with caution
        # if [ "$current_file_patched" = false ]; then
        #     log_warn "No specific patches applied, consider generic injection (currently not implemented)..."
        #     # Generic injection logic from original script could go here, but needs careful testing and validation
        # fi

        # Clean up backup
        if [ "<span class="math-inline">current\_file\_patched" \= true \]; then
sudo rm \-f "</span>{temp_file_path}.bak"
            ((modified_count++))
            log_info "File modified successfully: ${temp_file_path#"$temp_dir/"}"
        else
            log_warn "File was not modified (no applicable patch or failed): ${temp_file_path#"<span class="math-inline">temp\_dir/"\}"
\# Restore original file just in case there were partial failed modifications
sudo mv "</span>{temp_file_path}.bak" "$temp_file_path"
        fi
        echo " File ${temp_file_path#"$temp_dir/"} processing complete. Modified: $current_file_patched" >> "$LOG_FILE"
    done

    # 7. Check if any critical patches were successfully applied
    if [ "$patch_successful" = false ]; then
        log_error "Failed to successfully apply any critical JS patches. Cursor version might be incompatible."
        log_error "Aborting modification process. Application remains unchanged (original backup retained at $backup_app_path)."
        sudo rm -rf "$temp_dir" # Clean up temp directory
        return 1
    fi
    log_info "Successfully modified $modified_count JS files."

    # 8. Re-sign the application (ad-hoc, enhanced with retry and verification)
    log_info "Attempting ad-hoc signing of the modified application..."
    local max_retry=3
    local retry_count=0
    local sign_success=false

    while [ $retry_count -lt $max_retry ] && [ "$sign_success" = false ]; do
        ((retry_count++))
        log_info "Signing attempt $retry_count/$max_retry..."
        # Use --deep to sign all nested content, --force to overwrite existing signature (though removed), -s - for ad-hoc signing
        # Preserve metadata if possible, though ad-hoc signing might limit this
        if sudo codesign --sign - --force --deep --preserve-metadata=identifier,entitlements,flags "$temp_app_path" 2>> "$LOG_FILE"; then
            log_info "Signing command executed successfully, verifying..."
            # Verify signature
            # Use --strict and high verbosity for detailed check
            if sudo codesign --verify --strict --verbose=4 "$temp_app_path" 2>> "$LOG_FILE"; then
                log_info "Signature verification successful!"
                sign_success=true
            else
                log_warn "Signature verification failed (Attempt $retry_count). See log for details."
                # If verification fails, may need to remove signature before retrying
                # Corrected: Use || true
                (sudo codesign --remove-signature "$temp_app_path" 2>/dev/null) || true
                if [ $retry_count -lt $max_retry ]; then
                    sleep 1
                fi
            fi
        else
            log_warn "Signing command execution failed (Attempt $retry_count). See log for details."
            if [ $retry_count -lt $max_retry ]; then
                sleep 1
            fi
        fi
    done

    if [ "$sign_success" = false ]; then
        log_error "Application signing failed or could not be verified after $max_retry attempts."
        log_error "The modified application might not run or may show as damaged."
        log_error "You can try manual signing: sudo codesign --sign - --force --deep '$temp_app_path'"
        log_error "Or restore from backup: sudo cp -a '$backup_app_path' '$CURSOR_APP_PATH'"
        log_info "Temporary files kept at: $temp_dir"
        # Don't clean up automatically, let user decide how to proceed
        return 1
    fi

    # 9. Replace original application
    log_info "Signing successful, preparing to replace the application in /Applications..."
    # First remove the original application
    if sudo rm -rf "$CURSOR_APP_PATH"; then
        log_info "Original application removed."
        # Copy the modified application
        if sudo cp -a "$temp_app_path" "$CURSOR_APP_PATH"; then
            log_info "Successfully copied modified application to /Applications."
            # Set final application ownership and permissions
            sudo chown -R root:wheel "$CURSOR_APP_PATH"
            sudo chmod -R 755 "$CURSOR_APP_PATH"
            # Clean up quarantine attribute (ensure again)
            # Corrected: Use || for error handling
            (sudo xattr -rd com.apple.quarantine "$CURSOR_APP_PATH" 2>/dev/null) || log_warn "Error removing final app quarantine attribute or attribute not present."
            log_info "Application replacement and permission setup complete."
        else
            log_error "Failed to copy modified application back to /Applications!"
            log_error "System might be in a corrupted state! Attempting to restore from backup..."
            if sudo cp -a "$backup_app_path" "$CURSOR_APP_PATH"; then
                 log_info "Successfully restored original application from backup."
                 # Reset ownership/perms on restored backup too
                 sudo chown -R root:wheel "$CURSOR_APP_PATH"
                 sudo chmod -R 755 "$CURSOR_APP_PATH"
            else
                 log_error "Restoring original application from backup also failed! Please perform manual recovery."
                 log_error "Backup located at: $backup_app_path"
            fi
            sudo rm -rf "$temp_dir"
            return 1
        fi
    else
        log_error "Failed to remove original application: $CURSOR_APP_PATH"
        log_error "Cannot proceed with replacement."
        sudo rm -rf "$temp_dir"
        return 1
    fi

    # 10. Clean up temporary files
    log_info "Cleaning up temporary working directory: $temp_dir"
    sudo rm -rf "$temp_dir"

    log_info "Cursor main application file modification complete!"
    log_info "Original application backed up to: <span class="math-inline">backup\_app\_path"
return 0
\}
\# Show file tree structure \(simplified version\)
show\_file\_tree\(\) \{
echo
log\_info "Overview of relevant file structure\:"
echo \-e "</span>{BLUE}<span class="math-inline">CURSOR\_APP\_SUPPORT\_DIR</span>{NC}"
    echo "├── User/globalStorage/"
    if [ -f "$STORAGE_FILE" ]; then # Corrected: Added file check
        echo "│   ├── storage.json (Backup created)"
    else
        echo "│   ├── storage.json (Does not exist)"
    fi
    if [ -d "$BACKUP_DIR" ]; then # Corrected: Added directory check
        echo "│   └── backups/"
        # List max 5 backups, indenting output
        ls -1 "$BACKUP_DIR" | grep 'storage\.json\.backup_' | sed 's/^/│       └── /' | head -n 5
        if [ $(ls -1 "$BACKUP_DIR" | grep 'storage\.json\.backup_' | wc -l) -gt 5 ]; then
            echo "│           ..."
        fi
    fi
    local app_backup_dir="$CURSOR_APP_SUPPORT_DIR/backups/app_backups"
    if [ -d "$app_backup_dir" ]; then # Corrected: Added directory check
        echo "├── backups/app_backups/"
        # List max 3 app backups
        ls -1 "$app_backup_dir" | sed 's/^/│   └── /' | head -n 3
         if [ $(ls -1 "<span class="math-inline">app\_backup\_dir" \| wc \-l\) \-gt 3 \]; then
echo "│       \.\.\."
fi
fi
echo
\}
\# Show promotional info \(keep original intent\)
show\_follow\_info\(\) \{
echo
echo \-e "</span>{GREEN}================================<span class="math-inline">\{NC\}"
echo \-e "</span>{YELLOW}  Follow the WeChat Public Account [煎饼果子卷AI] for more Cursor tips and AI knowledge exchange. <span class="math-inline">\{NC\}"
echo \-e "</span>{YELLOW}  (Script is free, follow account/join group for more tips and experts) <span class="math-inline">\{NC\}"
echo \-e "</span>{GREEN}================================${NC}"
    echo
}

# Disable auto-update (enhanced version)
disable_auto_update() {
    # Corrected path for cache according to typical Electron app behavior
    local updater_cache_dir="$CURRENT_USER_HOME/Library/Caches/cursor-updater"
    local app_update_yml="$CURSOR_APP_PATH/Contents/Resources/app-update.yml"

    echo
    log_info "Attempting to disable Cursor auto-updates..."

    # Method 1: Modify app-update.yml (make it invalid or read-only)
    if [ -f "$app_update_yml" ]; then
        log_info "Processing <span class="math-inline">app\_update\_yml\.\.\."
local yml\_backup\="</span>{app_update_yml}.bak_$(date +%Y%m%d_%H%M%S)"
        log_info "Backing up to: $yml_backup"
        if sudo cp "$app_update_yml" "$yml_backup"; then
            log_info "Setting $app_update_yml to read-only..."
            # Set owner to root:wheel, permissions to 444 (read-only)
            sudo chown root:wheel "$app_update_yml"
            if sudo chmod 444 "$app_update_yml"; then
                log_info "Successfully set $app_update_yml to read-only."
                # Verify permissions
                ls -l "$app_update_yml" >> "$LOG_FILE"
            else
                log_error "Failed to set permissions for $app_update_yml!"
                log_warn "Attempting to restore backup..."
                # Ignore error if restore fails
                # Corrected: Use || for error handling
                (sudo mv "$yml_backup" "$app_update_yml") || log_error "Failed to restore backup!"
            fi
        else
            log_warn "Failed to back up $app_update_yml, skipping this method."
        fi
    else
        log